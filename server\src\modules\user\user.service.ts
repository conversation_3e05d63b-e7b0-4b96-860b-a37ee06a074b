import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User, UserDocument, UserEntity } from './entities/user.entity';
import { UserResponseDto } from './dto/user-response.dto';
import { CompleteLevelDto } from './dto/complete-level.dto';
import { formatDate } from '../../common/utils/date-formatter';
import { LevelService } from '../level/level.service';
import { WeixinUserBindDto } from '../weixin/dto/weixin-user-bind.dto';
import {
  WeixinUserInfoDto,
  WeixinLevelDto,
} from '../weixin/dto/weixin-user-info.dto';
import {
  WeixinLevelDetailDto,
  WeixinPhraseDto,
} from '../weixin/dto/weixin-level-detail.dto';
import {
  WeixinCompleteLevelDto,
  WeixinCompleteLevelResponseDto,
} from '../weixin/dto/weixin-complete-level.dto';
import {
  WeixinLoginDto,
  WeixinLoginResponseDto,
  WeixinPhoneBindDto,
} from '../weixin/dto/weixin-login.dto';
import {
  WeixinShareDto,
  WeixinShareResponseDto,
  WeixinDailyStatusDto,
} from '../weixin/dto/weixin-daily-play.dto';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @Inject(forwardRef(() => LevelService))
    private readonly levelService: LevelService,
  ) {}

  // 辅助方法：清理空字符串，将其转换为undefined
  private cleanEmptyStrings(value: string | undefined): string | undefined {
    return value === '' ? undefined : value;
  }

  // 生成8位唯一随机数字ID
  private async generateUniqueUserId(): Promise<string> {
    let id: string;
    do {
      // 生成8位随机数字（10000000-99999999）
      id = Math.floor(Math.random() * 90000000 + 10000000).toString();
    } while (await this.userModel.findOne({ id }).exec());
    return id;
  }

  private _mapToUserResponseDto(user: UserDocument): UserResponseDto {
    return {
      id: user.id,
      phone: user.phone,
      openid: user.openid,
      nickname: user.nickname,
      avatarUrl: user.avatarUrl,
      unlockedLevels: user.unlockedLevels,
      completedLevelIds: user.completedLevelIds,
      totalGames: user.totalGames,
      totalCompletions: user.totalCompletions,
      lastPlayTime: formatDate(user.lastPlayTime),
      isVip: user.isVip,
      vipExpiresAt: user.vipExpiresAt
        ? formatDate(user.vipExpiresAt)
        : undefined,
      dailyUnlockLimit: user.dailyUnlockLimit,
      dailyUnlockCount: user.dailyUnlockCount,
      dailyShared: user.dailyShared,
      lastPlayDate: user.lastPlayDate,
      totalShares: user.totalShares,
      createdAt: formatDate(user.createdAt),
      updatedAt: formatDate(user.updatedAt),
    };
  }

  async getUserEntity(id: string): Promise<UserDocument> {
    const user = await this.userModel.findOne({ id }).exec();
    if (!user) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的用户`);
    }
    return user;
  }

  async getUserByOpenid(openid: string): Promise<UserDocument | null> {
    if (!openid) return null;
    return this.userModel.findOne({ openid }).exec();
  }

  async getUserByPhone(phone: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ phone }).exec();
  }

  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    // 检查手机号是否已存在（如果提供了手机号）
    if (createUserDto.phone) {
      const existingUserByPhone = await this.getUserByPhone(
        createUserDto.phone,
      );
      if (existingUserByPhone) {
        throw new BadRequestException(`手机号 "${createUserDto.phone}" 已存在`);
      }
    }

    // 如果提供了openid，检查是否已存在
    if (createUserDto.openid) {
      const existingUserByOpenid = await this.getUserByOpenid(
        createUserDto.openid,
      );
      if (existingUserByOpenid) {
        throw new BadRequestException(
          `openid "${createUserDto.openid}" 已存在`,
        );
      }
    }

    // 至少需要提供手机号或openid中的一个
    if (!createUserDto.phone && !createUserDto.openid) {
      throw new BadRequestException('至少需要提供手机号或openid中的一个');
    }

    const today = new Date().toISOString().split('T')[0];

    const newUser = new this.userModel({
      id: await this.generateUniqueUserId(), // 使用8位随机数字ID
      phone: this.cleanEmptyStrings(createUserDto.phone),
      openid: this.cleanEmptyStrings(createUserDto.openid),
      nickname: this.cleanEmptyStrings(createUserDto.nickname),
      avatarUrl: this.cleanEmptyStrings(createUserDto.avatarUrl),
      unlockedLevels: 1, // 新用户默认开启第一关
      completedLevelIds: [],
      totalGames: 0,
      totalCompletions: 0,
      lastPlayTime: new Date(),
      isVip: false,
      dailyUnlockLimit: 15,
      dailyUnlockCount: 0,
      dailyShared: false,
      lastPlayDate: today,
      totalShares: 0,
    });

    const savedUser = await newUser.save();
    return this._mapToUserResponseDto(savedUser);
  }

  async findAll(params?: {
    search?: string;
    isVip?: boolean;
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  }): Promise<{ users: UserResponseDto[]; total: number }> {
    const {
      search,
      isVip,
      startDate,
      endDate,
      page = 1,
      pageSize = 20,
    } = params || {};

    // 构建查询条件
    const query: any = {};

    // 搜索条件（手机号、昵称、ID）
    if (search) {
      query.$or = [
        { phone: { $regex: search, $options: 'i' } },
        { nickname: { $regex: search, $options: 'i' } },
        { id: { $regex: search, $options: 'i' } },
      ];
    }

    // VIP状态过滤
    if (isVip !== undefined) {
      query.isVip = isVip;
    }

    // 日期范围过滤
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate + 'T23:59:59.999Z');
      }
    }

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 执行查询
    const [users, total] = await Promise.all([
      this.userModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(pageSize)
        .exec(),
      this.userModel.countDocuments(query).exec(),
    ]);

    // 检查并更新VIP状态
    const updatedUsers = await Promise.all(
      users.map(async (user) => {
        const updatedUser = await this.checkAndUpdateVipStatus(user);
        return this._mapToUserResponseDto(updatedUser);
      }),
    );

    return {
      users: updatedUsers,
      total,
    };
  }

  async findOne(id: string): Promise<UserResponseDto> {
    const user = await this.getUserEntity(id);
    return this._mapToUserResponseDto(user);
  }

  async findByOpenid(openid: string): Promise<UserResponseDto> {
    const user = await this.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
    }
    return this._mapToUserResponseDto(user);
  }

  async findByPhone(phone: string): Promise<UserResponseDto> {
    const user = await this.getUserByPhone(phone);
    if (!user) {
      throw new NotFoundException(`未找到手机号为 "${phone}" 的用户`);
    }
    return this._mapToUserResponseDto(user);
  }

  async update(
    id: string,
    updateUserDto: UpdateUserDto,
  ): Promise<UserResponseDto> {
    const existingUser = await this.getUserEntity(id);

    // 如果要更新手机号，检查新手机号是否已被其他用户使用
    if (updateUserDto.phone && updateUserDto.phone !== existingUser.phone) {
      const existingUserByPhone = await this.getUserByPhone(
        updateUserDto.phone,
      );
      if (existingUserByPhone && existingUserByPhone.id !== id) {
        throw new BadRequestException(
          `手机号 "${updateUserDto.phone}" 已被其他用户使用`,
        );
      }
    }

    // 如果要更新openid，检查新openid是否已被其他用户使用
    if (updateUserDto.openid && updateUserDto.openid !== existingUser.openid) {
      const existingUserByOpenid = await this.getUserByOpenid(
        updateUserDto.openid,
      );
      if (existingUserByOpenid && existingUserByOpenid.id !== id) {
        throw new BadRequestException(
          `openid "${updateUserDto.openid}" 已被其他用户使用`,
        );
      }
    }

    // 清理空字符串
    const cleanedUpdateDto = {
      ...updateUserDto,
      phone: updateUserDto.phone !== undefined ? this.cleanEmptyStrings(updateUserDto.phone) : undefined,
      openid: updateUserDto.openid !== undefined ? this.cleanEmptyStrings(updateUserDto.openid) : undefined,
      nickname: updateUserDto.nickname !== undefined ? this.cleanEmptyStrings(updateUserDto.nickname) : undefined,
      avatarUrl: updateUserDto.avatarUrl !== undefined ? this.cleanEmptyStrings(updateUserDto.avatarUrl) : undefined,
      updatedAt: new Date()
    };

    const updatedUser = await this.userModel
      .findOneAndUpdate(
        { id },
        cleanedUpdateDto,
        { new: true },
      )
      .exec();

    if (!updatedUser) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的用户`);
    }

    return this._mapToUserResponseDto(updatedUser);
  }

  async remove(id: string): Promise<void> {
    const result = await this.userModel.deleteOne({ id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的用户`);
    }
  }

  // 用户完成关卡
  async completeLevel(
    userId: string,
    completeLevelDto: CompleteLevelDto,
  ): Promise<UserResponseDto> {
    const user = await this.getUserEntity(userId);
    const { levelId } = completeLevelDto;

    // 验证关卡是否存在
    await this.levelService.getLevelEntity(levelId);

    // 检查用户是否已经完成过这个关卡
    if (user.completedLevelIds.includes(levelId)) {
      throw new BadRequestException(`用户已经完成过关卡 "${levelId}"`);
    }

    // 添加到已完成关卡列表
    user.completedLevelIds.push(levelId);
    user.totalCompletions += 1;
    user.totalGames += 1;
    user.lastPlayTime = new Date();

    // 如果完成的是当前最高关卡，解锁下一关（最多1000关）
    const currentLevel = user.completedLevelIds.length;
    if (currentLevel >= user.unlockedLevels && user.unlockedLevels < 1000) {
      user.unlockedLevels = Math.min(currentLevel + 1, 1000);
    }

    const updatedUser = await user.save();
    return this._mapToUserResponseDto(updatedUser);
  }

  // 用户开始游戏（增加游戏次数）
  async startGame(userId: string): Promise<UserResponseDto> {
    const user = await this.getUserEntity(userId);

    user.totalGames += 1;
    user.lastPlayTime = new Date();

    const updatedUser = await user.save();
    return this._mapToUserResponseDto(updatedUser);
  }

  // 获取用户游戏统计
  async getUserStats(userId: string): Promise<{
    totalGames: number;
    totalCompletions: number;
    unlockedLevels: number;
    completedLevels: number;
    completionRate: number;
  }> {
    const user = await this.getUserEntity(userId);

    return {
      totalGames: user.totalGames,
      totalCompletions: user.totalCompletions,
      unlockedLevels: user.unlockedLevels,
      completedLevels: user.completedLevelIds.length,
      completionRate:
        user.totalGames > 0
          ? (user.totalCompletions / user.totalGames) * 100
          : 0,
    };
  }

  // 重置用户进度
  async resetUserProgress(userId: string): Promise<UserResponseDto> {
    const user = await this.getUserEntity(userId);

    user.unlockedLevels = 1;
    user.completedLevelIds = [];
    user.totalGames = 0;
    user.totalCompletions = 0;

    const updatedUser = await user.save();
    return this._mapToUserResponseDto(updatedUser);
  }

  // 微信小程序相关方法

  // 手机号加密处理（中间4位用*代替）
  private maskPhone(phone?: string): string | undefined {
    if (!phone || phone.length !== 11) return phone;
    return phone.substring(0, 3) + '****' + phone.substring(7);
  }

  // 微信用户绑定
  async bindWeixinUser(bindDto: WeixinUserBindDto): Promise<WeixinUserInfoDto> {
    // 检查手机号是否已存在
    const existingUserByPhone = await this.getUserByPhone(bindDto.phone);
    if (existingUserByPhone) {
      throw new BadRequestException(`手机号 "${bindDto.phone}" 已存在`);
    }

    // 检查openid是否已存在
    const existingUserByOpenid = await this.getUserByOpenid(bindDto.openid);
    if (existingUserByOpenid) {
      throw new BadRequestException(`openid "${bindDto.openid}" 已存在`);
    }

    const today = new Date().toISOString().split('T')[0];

    const newUser = new this.userModel({
      id: await this.generateUniqueUserId(),
      phone: this.cleanEmptyStrings(bindDto.phone),
      openid: this.cleanEmptyStrings(bindDto.openid),
      nickname: this.cleanEmptyStrings(bindDto.nickname),
      avatarUrl: this.cleanEmptyStrings(bindDto.avatarUrl),
      unlockedLevels: 1,
      completedLevelIds: [],
      totalGames: 0,
      totalCompletions: 0,
      lastPlayTime: new Date(),
      isVip: false,
      dailyUnlockLimit: 15,
      dailyUnlockCount: 0,
      dailyShared: false,
      lastPlayDate: today,
      totalShares: 0,
    });

    const savedUser = await newUser.save();

    return {
      id: savedUser.id,
      maskedPhone: this.maskPhone(savedUser.phone),
      nickname: savedUser.nickname,
      avatarUrl: savedUser.avatarUrl,
      unlockedLevels: savedUser.unlockedLevels,
      completedLevelIds: savedUser.completedLevelIds,
      totalGames: savedUser.totalGames,
      totalCompletions: savedUser.totalCompletions,
      lastPlayTime: formatDate(savedUser.lastPlayTime),
      createdAt: formatDate(savedUser.createdAt),
    };
  }

  // 根据openid获取微信用户信息
  async getWeixinUserInfo(openid: string): Promise<WeixinUserInfoDto> {
    const user = await this.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
    }

    return {
      id: user.id,
      maskedPhone: this.maskPhone(user.phone),
      nickname: user.nickname,
      avatarUrl: user.avatarUrl,
      unlockedLevels: user.unlockedLevels,
      completedLevelIds: user.completedLevelIds,
      totalGames: user.totalGames,
      totalCompletions: user.totalCompletions,
      lastPlayTime: formatDate(user.lastPlayTime),
      createdAt: formatDate(user.createdAt),
    };
  }

  // 根据用户ID获取微信用户信息
  async getWeixinUserInfoById(id: string): Promise<WeixinUserInfoDto> {
    const user = await this.getUserEntity(id);

    return {
      id: user.id,
      maskedPhone: this.maskPhone(user.phone),
      nickname: user.nickname,
      avatarUrl: user.avatarUrl,
      unlockedLevels: user.unlockedLevels,
      completedLevelIds: user.completedLevelIds,
      totalGames: user.totalGames,
      totalCompletions: user.totalCompletions,
      lastPlayTime: formatDate(user.lastPlayTime),
      createdAt: formatDate(user.createdAt),
    };
  }

  // 获取微信关卡列表（包含用户进度）
  async getWeixinLevelsWithProgress(openid: string): Promise<WeixinLevelDto[]> {
    const user = await this.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
    }

    const allLevels = await this.levelService.findAll();

    return allLevels.map((level, index) => ({
      id: level.id,
      name: level.name,
      difficulty: level.difficulty,
      description: level.description,
      isUnlocked: index + 1 <= user.unlockedLevels,
      isCompleted: user.completedLevelIds.includes(level.id),
      createdAt: level.createdAt,
    }));
  }

  // 根据关卡ID获取微信小程序关卡详情（包含用户进度和词组信息）
  async getWeixinLevelDetail(
    openid: string,
    levelId: string,
  ): Promise<WeixinLevelDetailDto> {
    const user = await this.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
    }

    // 获取关卡详细信息（包含词组）
    const levelWithPhrases =
      await this.levelService.getLevelWithPhrases(levelId);

    // 检查用户是否有权限访问此关卡
    const allLevels = await this.levelService.findAll();
    const levelIndex = allLevels.findIndex((level) => level.id === levelId);

    if (levelIndex === -1) {
      throw new NotFoundException(`未找到 ID 为 "${levelId}" 的关卡`);
    }

    const isUnlocked = levelIndex + 1 <= user.unlockedLevels;
    if (!isUnlocked) {
      throw new BadRequestException(`关卡 "${levelId}" 尚未解锁`);
    }

    // 转换词组格式
    const phrases: WeixinPhraseDto[] = levelWithPhrases.phrases.map(
      (phrase) => ({
        id: phrase.id,
        text: phrase.text,
        meaning: phrase.meaning,
        exampleSentence: phrase.exampleSentence,
        tags: phrase.tags,
      }),
    );

    return {
      id: levelWithPhrases.id,
      name: levelWithPhrases.name,
      difficulty: levelWithPhrases.difficulty,
      description: levelWithPhrases.description,
      isUnlocked: true, // 已经检查过权限
      isCompleted: user.completedLevelIds.includes(levelId),
      phrases,
      createdAt: levelWithPhrases.createdAt,
    };
  }

  // 微信小程序用户通关关卡
  async weixinCompleteLevel(
    completeLevelDto: WeixinCompleteLevelDto,
  ): Promise<WeixinCompleteLevelResponseDto> {
    const { openid, levelId } = completeLevelDto;

    let user = await this.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
    }

    // 检查并重置每日数据
    user = await this.checkAndResetDailyData(user);

    // 检查每日解锁限制
    const { canUnlock, reason } = await this.canUserUnlock(user);
    if (!canUnlock) {
      throw new BadRequestException(reason);
    }

    // 验证关卡是否存在
    await this.levelService.getLevelEntity(levelId);

    // 检查用户是否已经完成过这个关卡
    if (user.completedLevelIds.includes(levelId)) {
      throw new BadRequestException(`用户已经完成过关卡 "${levelId}"`);
    }

    // 检查关卡是否已解锁
    const allLevels = await this.levelService.findAll();
    const levelIndex = allLevels.findIndex((level) => level.id === levelId);

    if (levelIndex === -1) {
      throw new NotFoundException(`未找到 ID 为 "${levelId}" 的关卡`);
    }

    const isUnlocked = levelIndex + 1 <= user.unlockedLevels;
    if (!isUnlocked) {
      throw new BadRequestException(`关卡 "${levelId}" 尚未解锁`);
    }

    // 添加到已完成关卡列表
    user.completedLevelIds.push(levelId);
    user.totalCompletions += 1;
    user.totalGames += 1;
    user.lastPlayTime = new Date();

    // 如果完成的是当前最高关卡，解锁下一关（最多1000关）
    const currentLevel = user.completedLevelIds.length;
    let hasUnlockedNewLevel = false;

    if (currentLevel >= user.unlockedLevels && user.unlockedLevels < 1000) {
      // 解锁新关卡需要消耗每日解锁次数
      user.unlockedLevels = Math.min(currentLevel + 1, 1000);
      user.dailyUnlockCount += 1;
      hasUnlockedNewLevel = true;
    }

    const updatedUser = await user.save();

    this.logger.log(
      `🎮 用户通关: userId=${updatedUser.id} levelId=${levelId} dailyUnlockCount=${updatedUser.dailyUnlockCount}/${updatedUser.dailyUnlockLimit}`,
    );

    const remainingUnlocks = updatedUser.isVip
      ? -1
      : Math.max(
          0,
          updatedUser.dailyUnlockLimit - updatedUser.dailyUnlockCount,
        );

    return {
      message: hasUnlockedNewLevel
        ? '恭喜！关卡通关成功，解锁新关卡！'
        : '恭喜！关卡通关成功！',
      userId: updatedUser.id,
      levelId,
      unlockedLevels: updatedUser.unlockedLevels,
      totalCompletions: updatedUser.totalCompletions,
      hasUnlockedNewLevel,
      dailyUnlockCount: updatedUser.dailyUnlockCount,
      dailyUnlockLimit: updatedUser.dailyUnlockLimit,
      remainingUnlocks,
      isVip: updatedUser.isVip,
    };
  }

  // 微信小程序登录（新版本，支持自动注册）
  async weixinLogin(
    loginDto: WeixinLoginDto,
    weixinApiData: { openid: string; sessionKey: string; unionid?: string },
  ): Promise<WeixinLoginResponseDto> {
    const { openid, sessionKey, unionid } = weixinApiData;

    // sessionKey用于后续的数据解密，这里先存储但不在响应中返回
    this.logger.debug(`🔐 会话密钥已获取: ${sessionKey.substring(0, 8)}...`);

    // 查找是否已存在该openid的用户
    const user = await this.getUserByOpenid(openid);

    if (user) {
      // 用户已存在，直接登录
      this.logger.log(`👤 用户登录: openid=${openid.substring(0, 8)}...`);

      // 更新最后登录时间
      user.lastPlayTime = new Date();
      await user.save();

      return {
        status: 'success',
        message: '登录成功',
        openid,
        unionid,
        userInfo: {
          id: user.id,
          maskedPhone: this.maskPhone(user.phone),
          nickname: user.nickname,
          avatarUrl: user.avatarUrl,
          unlockedLevels: user.unlockedLevels,
          completedLevelIds: user.completedLevelIds,
          totalGames: user.totalGames,
          totalCompletions: user.totalCompletions,
          lastPlayTime: formatDate(user.lastPlayTime),
          createdAt: formatDate(user.createdAt),
        },
      };
    } else {
      // 用户不存在，自动注册新用户
      if (loginDto.phone) {
        // 如果提供了手机号，检查手机号是否已被其他用户使用
        const existingUserByPhone = await this.getUserByPhone(loginDto.phone);
        if (existingUserByPhone) {
          throw new BadRequestException(
            `手机号 "${loginDto.phone}" 已被其他用户使用`,
          );
        }
      }

      // 自动注册新用户（无论是否提供手机号）
      const today = new Date().toISOString().split('T')[0];

      const newUser = new this.userModel({
        id: await this.generateUniqueUserId(),
        phone: this.cleanEmptyStrings(loginDto.phone), // 清理空字符串
        openid,
        nickname: this.cleanEmptyStrings(loginDto.nickname) || '微信用户',
        avatarUrl: this.cleanEmptyStrings(loginDto.avatarUrl),
        unlockedLevels: 1,
        completedLevelIds: [],
        totalGames: 0,
        totalCompletions: 0,
        lastPlayTime: new Date(),
        isVip: false,
        dailyUnlockLimit: 15,
        dailyUnlockCount: 0,
        dailyShared: false,
        lastPlayDate: today,
        totalShares: 0,
      });

      const savedUser = await newUser.save();
      this.logger.log(
        `🆕 新用户注册并登录: openid=${openid.substring(0, 8)}... phone=${loginDto.phone || '未提供'}`,
      );

      return {
        status: 'success',
        message: '注册并登录成功',
        openid,
        unionid,
        userInfo: {
          id: savedUser.id,
          maskedPhone: savedUser.phone
            ? this.maskPhone(savedUser.phone)
            : undefined,
          nickname: savedUser.nickname,
          avatarUrl: savedUser.avatarUrl,
          unlockedLevels: savedUser.unlockedLevels,
          completedLevelIds: savedUser.completedLevelIds,
          totalGames: savedUser.totalGames,
          totalCompletions: savedUser.totalCompletions,
          lastPlayTime: formatDate(savedUser.lastPlayTime),
          createdAt: formatDate(savedUser.createdAt),
        },
      };
    }
  }

  // 微信用户绑定手机号（用于登录后的绑定流程）
  async weixinBindPhone(
    bindDto: WeixinPhoneBindDto,
  ): Promise<WeixinLoginResponseDto> {
    const { openid, phone, nickname, avatarUrl } = bindDto;

    // 检查openid是否已存在用户
    const existingUserByOpenid = await this.getUserByOpenid(openid);
    if (existingUserByOpenid) {
      throw new BadRequestException(`该微信账号已绑定用户`);
    }

    // 检查手机号是否已被使用
    const existingUserByPhone = await this.getUserByPhone(phone);
    if (existingUserByPhone) {
      throw new BadRequestException(`手机号 "${phone}" 已被其他用户使用`);
    }

    // 创建新用户
    const today = new Date().toISOString().split('T')[0];

    const newUser = new this.userModel({
      id: await this.generateUniqueUserId(),
      phone: this.cleanEmptyStrings(phone),
      openid,
      nickname: this.cleanEmptyStrings(nickname) || '微信用户',
      avatarUrl: this.cleanEmptyStrings(avatarUrl),
      unlockedLevels: 1,
      completedLevelIds: [],
      totalGames: 0,
      totalCompletions: 0,
      lastPlayTime: new Date(),
      isVip: false,
      dailyUnlockLimit: 15,
      dailyUnlockCount: 0,
      dailyShared: false,
      lastPlayDate: today,
      totalShares: 0,
    });

    const savedUser = await newUser.save();
    this.logger.log(
      `📱 用户绑定手机号成功: openid=${openid.substring(0, 8)}... phone=${phone}`,
    );

    return {
      status: 'success',
      message: '绑定成功',
      openid,
      userInfo: {
        id: savedUser.id,
        maskedPhone: this.maskPhone(savedUser.phone),
        nickname: savedUser.nickname,
        avatarUrl: savedUser.avatarUrl,
        unlockedLevels: savedUser.unlockedLevels,
        completedLevelIds: savedUser.completedLevelIds,
        totalGames: savedUser.totalGames,
        totalCompletions: savedUser.totalCompletions,
        lastPlayTime: formatDate(savedUser.lastPlayTime),
        createdAt: formatDate(savedUser.createdAt),
      },
    };
  }

  // 检查并重置每日数据
  private async checkAndResetDailyData(
    user: UserDocument,
  ): Promise<UserDocument> {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式

    if (user.lastPlayDate !== today) {
      // 新的一天，重置每日数据
      user.dailyUnlockCount = 0;
      user.dailyUnlockLimit = 15; // 重置为默认15次
      user.dailyShared = false;
      user.lastPlayDate = today;

      this.logger.log(`🔄 重置用户每日数据: userId=${user.id} date=${today}`);
      return await user.save();
    }

    return user;
  }

  // 检查并更新用户VIP状态
  private async checkAndUpdateVipStatus(
    user: UserDocument,
  ): Promise<UserDocument> {
    if (user.isVip && user.vipExpiresAt) {
      const now = new Date();
      if (now > user.vipExpiresAt) {
        // VIP已过期，更新状态
        user.isVip = false;
        user.vipExpiresAt = undefined;
        user.dailyUnlockLimit = 15; // 恢复普通用户限制
        await user.save();

        this.logger.log(
          `用户VIP已过期: userId=${user.id}, 过期时间=${user.vipExpiresAt}`,
        );
      }
    }
    return user;
  }

  // 检查用户是否可以解锁关卡
  private async canUserUnlock(
    user: UserDocument,
  ): Promise<{ canUnlock: boolean; reason?: string }> {
    // 先检查VIP状态
    user = await this.checkAndUpdateVipStatus(user);

    // VIP用户无限制
    if (user.isVip) {
      return { canUnlock: true };
    }

    // 检查每日解锁次数
    if (user.dailyUnlockCount >= user.dailyUnlockLimit) {
      return {
        canUnlock: false,
        reason: `今日解锁次数已达上限（${user.dailyUnlockLimit}次），请明天再来或分享获得额外机会`,
      };
    }

    return { canUnlock: true };
  }

  // 获取用户每日状态
  async getWeixinDailyStatus(openid: string): Promise<WeixinDailyStatusDto> {
    let user = await this.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
    }

    // 检查并重置每日数据
    user = await this.checkAndResetDailyData(user);

    // 检查VIP状态和解锁权限
    const { canUnlock, reason } = await this.canUserUnlock(user);
    const remainingUnlocks = user.isVip
      ? -1
      : Math.max(0, user.dailyUnlockLimit - user.dailyUnlockCount);

    return {
      id: user.id,
      dailyUnlockCount: user.dailyUnlockCount,
      dailyUnlockLimit: user.dailyUnlockLimit,
      remainingUnlocks,
      dailyShared: user.dailyShared,
      isVip: user.isVip,
      lastPlayDate: user.lastPlayDate,
      totalShares: user.totalShares,
      canUnlock,
      limitReason: reason,
    };
  }

  // 微信用户分享获得额外通关机会
  async weixinShare(shareDto: WeixinShareDto): Promise<WeixinShareResponseDto> {
    const { openid } = shareDto;

    let user = await this.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException(`未找到 openid 为 "${openid}" 的用户`);
    }

    // 检查并重置每日数据
    user = await this.checkAndResetDailyData(user);

    // 检查今日是否已分享
    if (user.dailyShared) {
      const remainingUnlocks = user.isVip
        ? -1
        : Math.max(0, user.dailyUnlockLimit - user.dailyUnlockCount);

      return {
        status: 'already_shared',
        message: '今日已分享过，无法重复获得奖励',
        userId: user.id,
        dailyUnlockCount: user.dailyUnlockCount,
        dailyUnlockLimit: user.dailyUnlockLimit,
        remainingUnlocks,
        isVip: user.isVip,
        totalShares: user.totalShares,
      };
    }

    // 分享成功，增加解锁次数和分享记录
    user.dailyShared = true;
    user.totalShares += 1;
    user.dailyUnlockLimit += 5; // 当日增加5次解锁机会

    const updatedUser = await user.save();
    const remainingUnlocks = updatedUser.isVip
      ? -1
      : Math.max(
          0,
          updatedUser.dailyUnlockLimit - updatedUser.dailyUnlockCount,
        );

    this.logger.log(
      `📤 用户分享成功: userId=${updatedUser.id} totalShares=${updatedUser.totalShares}`,
    );

    return {
      status: 'success',
      message: '分享成功，获得5次额外解锁机会！',
      userId: updatedUser.id,
      dailyUnlockCount: updatedUser.dailyUnlockCount,
      dailyUnlockLimit: updatedUser.dailyUnlockLimit,
      remainingUnlocks,
      isVip: updatedUser.isVip,
      totalShares: updatedUser.totalShares,
    };
  }
}
