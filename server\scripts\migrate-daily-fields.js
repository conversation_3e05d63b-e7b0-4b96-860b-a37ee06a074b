/**
 * 数据库迁移脚本：将每日通关限制字段迁移为每日解锁限制字段
 * 
 * 迁移内容：
 * - dailyPlayLimit -> dailyUnlockLimit
 * - dailyPlayCount -> dailyUnlockCount
 * 
 * 运行方式：
 * node scripts/migrate-daily-fields.js
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || '**************************************************';
const DATABASE_NAME = process.env.DATABASE_NAME || 'vocabulary-game';

async function migrateDailyFields() {
  let client;
  
  try {
    console.log('🚀 开始数据库迁移...');
    console.log(`📍 连接数据库: ${MONGODB_URI}`);
    
    // 连接数据库
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const usersCollection = db.collection('users');
    
    // 检查集合是否存在
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      console.log('⚠️  用户集合不存在，跳过迁移');
      return;
    }
    
    // 获取所有用户
    const users = await usersCollection.find({}).toArray();
    console.log(`📊 找到 ${users.length} 个用户需要迁移`);
    
    if (users.length === 0) {
      console.log('✅ 没有用户数据需要迁移');
      return;
    }
    
    let migratedCount = 0;
    let skippedCount = 0;
    
    // 批量更新操作
    const bulkOps = [];
    
    for (const user of users) {
      const updateFields = {};
      let needsUpdate = false;
      
      // 检查是否需要迁移 dailyPlayLimit -> dailyUnlockLimit
      if (user.hasOwnProperty('dailyPlayLimit') && !user.hasOwnProperty('dailyUnlockLimit')) {
        updateFields.dailyUnlockLimit = user.dailyPlayLimit || 15;
        needsUpdate = true;
      }
      
      // 检查是否需要迁移 dailyPlayCount -> dailyUnlockCount
      if (user.hasOwnProperty('dailyPlayCount') && !user.hasOwnProperty('dailyUnlockCount')) {
        updateFields.dailyUnlockCount = user.dailyPlayCount || 0;
        needsUpdate = true;
      }
      
      // 确保新字段存在（为没有旧字段的用户设置默认值）
      if (!user.hasOwnProperty('dailyUnlockLimit')) {
        updateFields.dailyUnlockLimit = 15;
        needsUpdate = true;
      }
      
      if (!user.hasOwnProperty('dailyUnlockCount')) {
        updateFields.dailyUnlockCount = 0;
        needsUpdate = true;
      }
      
      // 确保其他必要字段存在
      if (!user.hasOwnProperty('isVip')) {
        updateFields.isVip = false;
        needsUpdate = true;
      }
      
      if (!user.hasOwnProperty('dailyShared')) {
        updateFields.dailyShared = false;
        needsUpdate = true;
      }
      
      if (!user.hasOwnProperty('totalShares')) {
        updateFields.totalShares = 0;
        needsUpdate = true;
      }
      
      if (!user.hasOwnProperty('lastPlayDate')) {
        updateFields.lastPlayDate = new Date().toISOString().split('T')[0];
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        bulkOps.push({
          updateOne: {
            filter: { _id: user._id },
            update: { $set: updateFields }
          }
        });
        migratedCount++;
      } else {
        skippedCount++;
      }
    }
    
    // 执行批量更新
    if (bulkOps.length > 0) {
      console.log(`🔄 执行批量更新，共 ${bulkOps.length} 个操作...`);
      const result = await usersCollection.bulkWrite(bulkOps);
      console.log(`✅ 批量更新完成: 修改了 ${result.modifiedCount} 个文档`);
    }
    
    // 清理旧字段（可选，谨慎操作）
    console.log('🧹 开始清理旧字段...');
    const cleanupResult = await usersCollection.updateMany(
      {},
      { 
        $unset: { 
          dailyPlayLimit: "",
          dailyPlayCount: ""
        } 
      }
    );
    console.log(`🗑️  清理旧字段完成: 修改了 ${cleanupResult.modifiedCount} 个文档`);
    
    // 验证迁移结果
    console.log('🔍 验证迁移结果...');
    const verificationUsers = await usersCollection.find({}).limit(5).toArray();
    
    for (const user of verificationUsers) {
      console.log(`👤 用户 ${user.id || user._id}:`);
      console.log(`   - dailyUnlockLimit: ${user.dailyUnlockLimit}`);
      console.log(`   - dailyUnlockCount: ${user.dailyUnlockCount}`);
      console.log(`   - isVip: ${user.isVip}`);
      console.log(`   - dailyShared: ${user.dailyShared}`);
      console.log(`   - totalShares: ${user.totalShares}`);
      console.log(`   - lastPlayDate: ${user.lastPlayDate}`);
      
      // 检查是否还有旧字段
      if (user.hasOwnProperty('dailyPlayLimit') || user.hasOwnProperty('dailyPlayCount')) {
        console.log(`   ⚠️  仍存在旧字段`);
      }
    }
    
    console.log('\n📈 迁移统计:');
    console.log(`   - 总用户数: ${users.length}`);
    console.log(`   - 已迁移: ${migratedCount}`);
    console.log(`   - 已跳过: ${skippedCount}`);
    console.log('✅ 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移过程中发生错误:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  try {
    await migrateDailyFields();
    process.exit(0);
  } catch (error) {
    console.error('💥 迁移失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { migrateDailyFields };
