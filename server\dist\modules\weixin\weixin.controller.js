"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_service_1 = require("../user/user.service");
const level_service_1 = require("../level/level.service");
const weixin_user_bind_dto_1 = require("./dto/weixin-user-bind.dto");
const weixin_user_info_dto_1 = require("./dto/weixin-user-info.dto");
const weixin_level_detail_dto_1 = require("./dto/weixin-level-detail.dto");
const weixin_complete_level_dto_1 = require("./dto/weixin-complete-level.dto");
const weixin_login_dto_1 = require("./dto/weixin-login.dto");
const weixin_daily_play_dto_1 = require("./dto/weixin-daily-play.dto");
const weixin_share_config_dto_1 = require("./dto/weixin-share-config.dto");
const weixin_settings_dto_1 = require("./dto/weixin-settings.dto");
const weixin_global_config_dto_1 = require("./dto/weixin-global-config.dto");
const weixin_api_service_1 = require("./services/weixin-api.service");
const share_service_1 = require("../share/share.service");
const payment_service_1 = require("../payment/payment.service");
const settings_service_1 = require("../settings/settings.service");
const payment_dto_1 = require("../payment/dto/payment.dto");
let WeixinController = class WeixinController {
    userService;
    levelService;
    weixinApiService;
    shareService;
    paymentService;
    settingsService;
    constructor(userService, levelService, weixinApiService, shareService, paymentService, settingsService) {
        this.userService = userService;
        this.levelService = levelService;
        this.weixinApiService = weixinApiService;
        this.shareService = shareService;
        this.paymentService = paymentService;
        this.settingsService = settingsService;
    }
    async login(loginDto) {
        try {
            console.log(`🔐 微信登录尝试: code=${loginDto.code?.substring(0, 8)}..., phone=${loginDto.phone ? '已提供' : '未提供'}, nickname=${loginDto.nickname || '未提供'}`);
            const weixinData = await this.weixinApiService.code2Session(loginDto.code);
            const loginData = {
                openid: weixinData.openid,
                sessionKey: weixinData.session_key,
                unionid: weixinData.unionid,
            };
            const result = await this.userService.weixinLogin(loginDto, loginData);
            console.log(`✅ 微信登录成功: openid=${weixinData.openid.substring(0, 8)}..., status=${result.status}`);
            return result;
        }
        catch (error) {
            console.error(`❌ 微信登录失败: ${error.message}`);
            throw error;
        }
    }
    async bindPhone(bindDto) {
        return this.userService.weixinBindPhone(bindDto);
    }
    async getConfig() {
        const config = this.weixinApiService.getConfig();
        return {
            ...config,
            paymentOrderExpireMinutes: this.paymentService.getPaymentOrderExpireMinutes(),
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
        };
    }
    async getGlobalConfig() {
        try {
            const weixinConfig = this.weixinApiService.getConfig();
            const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([
                this.settingsService.findByKey('help_url').catch(() => null),
                this.settingsService
                    .findByKey('background_music_url')
                    .catch(() => null),
            ]);
            const globalConfig = {
                appId: weixinConfig.appId,
                isConfigured: weixinConfig.isConfigured,
                paymentOrderExpireMinutes: this.paymentService.getPaymentOrderExpireMinutes(),
                environment: process.env.NODE_ENV || 'development',
                timestamp: new Date().toISOString(),
                helpUrl: helpUrlSetting?.value || 'https://help.example.com',
                backgroundMusicUrl: backgroundMusicSetting?.value ||
                    'https://music.example.com/background.mp3',
                version: process.env.APP_VERSION || '1.0.0',
                features: {
                    enablePayment: weixinConfig.isConfigured,
                    enableShare: true,
                    enableVip: true,
                    enableMusic: true,
                },
                gameConfig: {
                    maxLevels: 1000,
                    dailyUnlockLimit: 15,
                    shareRewardCount: 5,
                },
            };
            return globalConfig;
        }
        catch (error) {
            console.error('获取全局配置失败:', error);
            return {
                appId: '',
                isConfigured: false,
                paymentOrderExpireMinutes: 30,
                environment: process.env.NODE_ENV || 'development',
                timestamp: new Date().toISOString(),
                helpUrl: 'https://help.example.com',
                backgroundMusicUrl: 'https://music.example.com/background.mp3',
                version: '1.0.0',
                features: {
                    enablePayment: false,
                    enableShare: true,
                    enableVip: true,
                    enableMusic: true,
                },
                gameConfig: {
                    maxLevels: 1000,
                    dailyUnlockLimit: 15,
                    shareRewardCount: 5,
                },
            };
        }
    }
    async getAppSettings() {
        try {
            const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([
                this.settingsService.findByKey('help_url').catch(() => null),
                this.settingsService
                    .findByKey('background_music_url')
                    .catch(() => null),
            ]);
            return {
                helpUrl: helpUrlSetting?.value || 'https://help.example.com',
                backgroundMusicUrl: backgroundMusicSetting?.value ||
                    'https://music.example.com/background.mp3',
            };
        }
        catch (error) {
            return {
                helpUrl: 'https://help.example.com',
                backgroundMusicUrl: 'https://music.example.com/background.mp3',
            };
        }
    }
    async bindUser(bindDto) {
        return this.userService.bindWeixinUser(bindDto);
    }
    async getUserInfo(openid) {
        return this.userService.getWeixinUserInfo(openid);
    }
    async getLevelsWithProgress(openid) {
        return this.userService.getWeixinLevelsWithProgress(openid);
    }
    async getUserInfoById(id) {
        return this.userService.getWeixinUserInfoById(id);
    }
    async getLevelDetail(levelId, openid) {
        return this.userService.getWeixinLevelDetail(openid, levelId);
    }
    async completeLevel(completeLevelDto) {
        return this.userService.weixinCompleteLevel(completeLevelDto);
    }
    async getDailyStatus(openid) {
        return this.userService.getWeixinDailyStatus(openid);
    }
    async share(shareDto) {
        return this.userService.weixinShare(shareDto);
    }
    async getShareConfig() {
        const activeConfigs = await this.shareService.findActive();
        const defaultConfig = activeConfigs.find((config) => config.type === 'default');
        const otherConfigs = activeConfigs.filter((config) => config.type !== 'default');
        const mapToWeixinConfig = (config) => ({
            title: config.title,
            path: config.path,
            imageUrl: config.imageUrl,
            description: config.description,
            type: config.type,
        });
        return {
            default: defaultConfig
                ? mapToWeixinConfig(defaultConfig)
                : {
                    title: '一起来挑战词汇游戏！',
                    path: '/pages/index/index',
                    description: '挑战你的词汇量，看看你能通过多少关！',
                    type: 'default',
                },
            configs: otherConfigs.map(mapToWeixinConfig),
            total: activeConfigs.length,
        };
    }
    async getShareConfigByType(type) {
        const config = await this.shareService.findByType(type);
        if (!config) {
            return null;
        }
        return {
            title: config.title,
            path: config.path,
            imageUrl: config.imageUrl,
            description: config.description,
            type: config.type,
        };
    }
    async getVipPackages() {
        return this.paymentService.getVipPackages();
    }
    async createPayment(body) {
        const { openid, packageId } = body;
        if (!openid || !packageId) {
            throw new common_1.BadRequestException('openid和packageId不能为空');
        }
        return this.paymentService.createPaymentOrder(openid, packageId);
    }
    async getPaymentOrders(openid) {
        if (!openid) {
            throw new common_1.BadRequestException('openid不能为空');
        }
        const user = await this.userService.findByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        return this.paymentService.getUserPaymentOrders(user.id);
    }
    async getPaymentStatus(out_trade_no) {
        return this.paymentService.queryPaymentOrder(out_trade_no);
    }
};
exports.WeixinController = WeixinController;
__decorate([
    (0, common_1.Post)('login'),
    (0, swagger_1.ApiOperation)({ summary: '微信小程序登录' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '登录成功或需要绑定手机号',
        type: weixin_login_dto_1.WeixinLoginResponseDto,
        schema: {
            examples: {
                loginSuccess: {
                    summary: '登录成功',
                    value: {
                        status: 'success',
                        message: '登录成功',
                        openid: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE',
                        userInfo: {
                            id: '12345678',
                            maskedPhone: '138****8000',
                            nickname: '微信用户',
                            avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/xxx',
                            unlockedLevels: 5,
                            completedLevelIds: ['1', '2', '3'],
                            totalGames: 20,
                            totalCompletions: 15,
                            lastPlayTime: '2025-06-18T12:00:00.000Z',
                            createdAt: '2025-06-18T10:00:00.000Z',
                        },
                    },
                },
                needBind: {
                    summary: '需要绑定手机号',
                    value: {
                        status: 'need_bind',
                        message: '请绑定手机号完成注册',
                        openid: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE',
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '登录凭证无效或微信接口调用失败',
        schema: {
            examples: {
                invalidCode: {
                    summary: '登录凭证无效',
                    value: {
                        statusCode: 400,
                        message: '微信登录失败: 登录凭证无效或已过期，请重新登录 (错误码: 40029)',
                        error: 'Bad Request',
                    },
                },
                configError: {
                    summary: '配置错误',
                    value: {
                        statusCode: 400,
                        message: '微信登录失败: AppID无效，请检查小程序配置 (错误码: 40013)',
                        error: 'Bad Request',
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [weixin_login_dto_1.WeixinLoginDto]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('bind-phone'),
    (0, swagger_1.ApiOperation)({ summary: '微信用户绑定手机号' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '绑定成功',
        type: weixin_login_dto_1.WeixinLoginResponseDto,
        schema: {
            example: {
                status: 'success',
                message: '绑定成功',
                openid: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE',
                userInfo: {
                    id: '12345678',
                    maskedPhone: '138****8000',
                    nickname: '微信用户',
                    avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/xxx',
                    unlockedLevels: 1,
                    completedLevelIds: [],
                    totalGames: 0,
                    totalCompletions: 0,
                    lastPlayTime: '2025-06-18T12:00:00.000Z',
                    createdAt: '2025-06-18T12:00:00.000Z',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '手机号已被使用或openid已绑定用户' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [weixin_login_dto_1.WeixinPhoneBindDto]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "bindPhone", null);
__decorate([
    (0, common_1.Get)('config'),
    (0, swagger_1.ApiOperation)({ summary: '获取微信小程序配置状态（调试用）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '配置状态',
        schema: {
            example: {
                appId: 'wx1234567890abcdef...',
                appSecret: '已配置',
                isConfigured: true,
                paymentOrderExpireMinutes: 30,
                timestamp: '2025-06-21T10:30:00.000Z',
                environment: 'development',
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getConfig", null);
__decorate([
    (0, common_1.Get)('global-config'),
    (0, swagger_1.ApiOperation)({ summary: '获取微信小程序全局配置' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '全局配置获取成功',
        type: weixin_global_config_dto_1.WeixinGlobalConfigDto,
        schema: {
            example: {
                appId: 'wx1234567890abcdef',
                isConfigured: true,
                paymentOrderExpireMinutes: 30,
                environment: 'development',
                timestamp: '2024-01-01T12:00:00.000Z',
                helpUrl: 'https://help.example.com',
                backgroundMusicUrl: 'https://music.example.com/background.mp3',
                version: '1.0.0',
                features: {
                    enablePayment: true,
                    enableShare: true,
                    enableVip: true,
                    enableMusic: true,
                },
                gameConfig: {
                    maxLevels: 1000,
                    dailyUnlockLimit: 15,
                    shareRewardCount: 5,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '获取配置失败' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getGlobalConfig", null);
__decorate([
    (0, common_1.Get)('app-settings'),
    (0, swagger_1.ApiOperation)({ summary: '获取小程序应用设置' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '应用设置获取成功',
        type: weixin_settings_dto_1.WeixinAppSettingsDto,
        schema: {
            example: {
                helpUrl: 'https://help.example.com',
                backgroundMusicUrl: 'https://music.example.com/background.mp3',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '获取设置失败' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getAppSettings", null);
__decorate([
    (0, common_1.Post)('user/bind'),
    (0, swagger_1.ApiOperation)({ summary: '微信小程序用户绑定' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '用户绑定成功',
        type: weixin_user_info_dto_1.WeixinUserInfoDto,
        schema: {
            example: {
                id: '12345678',
                maskedPhone: '138****8000',
                nickname: '微信用户',
                avatarUrl: 'https://wx.qlogo.cn/mmopen/...',
                unlockedLevels: 1,
                completedLevelIds: [],
                totalGames: 0,
                totalCompletions: 0,
                lastPlayTime: '2025-06-18T12:00:00.000Z',
                createdAt: '2025-06-18T12:00:00.000Z',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '手机号或openid已存在，或参数无效' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [weixin_user_bind_dto_1.WeixinUserBindDto]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "bindUser", null);
__decorate([
    (0, common_1.Get)('user/info'),
    (0, swagger_1.ApiOperation)({ summary: '根据openid获取微信小程序用户信息' }),
    (0, swagger_1.ApiQuery)({
        name: 'openid',
        description: '微信用户openid',
        example: 'wx_openid_123456',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户信息获取成功',
        type: weixin_user_info_dto_1.WeixinUserInfoDto,
        schema: {
            example: {
                id: '12345678',
                maskedPhone: '138****8000',
                nickname: '微信用户',
                avatarUrl: 'https://wx.qlogo.cn/mmopen/...',
                unlockedLevels: 5,
                completedLevelIds: ['1', '2', '3'],
                totalGames: 20,
                totalCompletions: 15,
                lastPlayTime: '2025-06-18T12:00:00.000Z',
                createdAt: '2025-06-18T10:00:00.000Z',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Query)('openid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getUserInfo", null);
__decorate([
    (0, common_1.Get)('levels'),
    (0, swagger_1.ApiOperation)({ summary: '获取微信小程序关卡列表（包含用户进度）' }),
    (0, swagger_1.ApiQuery)({
        name: 'openid',
        description: '微信用户openid',
        example: 'wx_openid_123456',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '关卡列表获取成功',
        type: [weixin_user_info_dto_1.WeixinLevelDto],
        schema: {
            example: [
                {
                    id: 'level-uuid-1',
                    name: '第1关 - 基础词汇',
                    difficulty: 1,
                    description: '这是第一关，包含基础词汇',
                    isUnlocked: true,
                    isCompleted: true,
                    createdAt: '2025-06-18T10:00:00.000Z',
                },
                {
                    id: 'level-uuid-2',
                    name: '第2关 - 进阶词汇',
                    difficulty: 2,
                    description: '这是第二关，包含进阶词汇',
                    isUnlocked: true,
                    isCompleted: false,
                    createdAt: '2025-06-18T10:00:00.000Z',
                },
                {
                    id: 'level-uuid-3',
                    name: '第3关 - 高级词汇',
                    difficulty: 3,
                    description: '这是第三关，包含高级词汇',
                    isUnlocked: false,
                    isCompleted: false,
                    createdAt: '2025-06-18T10:00:00.000Z',
                },
            ],
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Query)('openid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getLevelsWithProgress", null);
__decorate([
    (0, common_1.Get)('user/:id/info'),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID获取微信小程序用户信息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID', example: '12345678' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户信息获取成功',
        type: weixin_user_info_dto_1.WeixinUserInfoDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getUserInfoById", null);
__decorate([
    (0, common_1.Get)('level/:levelId'),
    (0, swagger_1.ApiOperation)({ summary: '根据关卡ID获取微信小程序关卡详情（包含词组信息）' }),
    (0, swagger_1.ApiParam)({ name: 'levelId', description: '关卡ID', example: 'level-uuid-1' }),
    (0, swagger_1.ApiQuery)({
        name: 'openid',
        description: '微信用户openid',
        example: 'wx_openid_123456',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '关卡详情获取成功',
        type: weixin_level_detail_dto_1.WeixinLevelDetailDto,
        schema: {
            example: {
                id: 'level-uuid-1',
                name: '第1关 - 基础词汇',
                difficulty: 1,
                description: '这是第一关，包含基础词汇',
                isUnlocked: true,
                isCompleted: false,
                phrases: [
                    {
                        id: 'phrase-uuid-1',
                        text: 'Hello World',
                        meaning: '你好，世界',
                        exampleSentence: 'When you start programming, the first thing you often do is print "Hello World".',
                        tags: ['greeting', 'common'],
                    },
                    {
                        id: 'phrase-uuid-2',
                        text: 'Good Morning',
                        meaning: '早上好',
                        exampleSentence: 'Good morning! How are you today?',
                        tags: ['greeting', 'daily'],
                    },
                ],
                createdAt: '2025-06-18T10:00:00.000Z',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在或关卡不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '关卡尚未解锁' }),
    __param(0, (0, common_1.Param)('levelId')),
    __param(1, (0, common_1.Query)('openid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getLevelDetail", null);
__decorate([
    (0, common_1.Post)('level/complete'),
    (0, swagger_1.ApiOperation)({ summary: '微信小程序用户通关关卡' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '关卡通关成功',
        type: weixin_complete_level_dto_1.WeixinCompleteLevelResponseDto,
        schema: {
            example: {
                message: '恭喜！关卡通关成功，解锁新关卡！',
                userId: '12345678',
                levelId: 'level-uuid-1',
                unlockedLevels: 2,
                totalCompletions: 1,
                hasUnlockedNewLevel: true,
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '关卡已完成、尚未解锁或参数无效' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在或关卡不存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [weixin_complete_level_dto_1.WeixinCompleteLevelDto]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "completeLevel", null);
__decorate([
    (0, common_1.Get)('daily-status'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户每日通关状态' }),
    (0, swagger_1.ApiQuery)({
        name: 'openid',
        description: '微信用户openid',
        example: 'wx_openid_123456',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '每日状态获取成功',
        type: weixin_daily_play_dto_1.WeixinDailyStatusDto,
        schema: {
            example: {
                id: '12345678',
                dailyPlayCount: 3,
                dailyPlayLimit: 15,
                remainingPlays: 12,
                dailyShared: false,
                isVip: false,
                lastPlayDate: '2025-06-19',
                totalShares: 5,
                canPlay: true,
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Query)('openid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getDailyStatus", null);
__decorate([
    (0, common_1.Post)('share'),
    (0, swagger_1.ApiOperation)({ summary: '用户分享获得额外通关机会' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '分享成功或已分享',
        type: weixin_daily_play_dto_1.WeixinShareResponseDto,
        schema: {
            examples: {
                shareSuccess: {
                    summary: '分享成功',
                    value: {
                        status: 'success',
                        message: '分享成功，获得5次额外通关机会！',
                        userId: '12345678',
                        dailyPlayCount: 3,
                        dailyPlayLimit: 20,
                        remainingPlays: 17,
                        isVip: false,
                        totalShares: 6,
                    },
                },
                alreadyShared: {
                    summary: '今日已分享',
                    value: {
                        status: 'already_shared',
                        message: '今日已分享过，无法重复获得奖励',
                        userId: '12345678',
                        dailyPlayCount: 3,
                        dailyPlayLimit: 15,
                        remainingPlays: 12,
                        isVip: false,
                        totalShares: 5,
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [weixin_daily_play_dto_1.WeixinShareDto]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "share", null);
__decorate([
    (0, common_1.Get)('share-config'),
    (0, swagger_1.ApiOperation)({ summary: '获取微信小程序分享配置' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分享配置获取成功',
        type: weixin_share_config_dto_1.WeixinShareConfigListDto,
        schema: {
            example: {
                default: {
                    title: '一起来挑战词汇游戏！',
                    path: '/pages/index/index',
                    imageUrl: 'https://example.com/share.jpg',
                    description: '挑战你的词汇量，看看你能通过多少关！',
                    type: 'default',
                },
                configs: [
                    {
                        title: '我在词汇游戏中获得了高分！',
                        path: '/pages/result/result',
                        imageUrl: 'https://example.com/result-share.jpg',
                        description: '快来挑战我的记录吧！',
                        type: 'result',
                    },
                ],
                total: 2,
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getShareConfig", null);
__decorate([
    (0, common_1.Get)('share-config/:type'),
    (0, swagger_1.ApiOperation)({ summary: '根据类型获取微信小程序分享配置' }),
    (0, swagger_1.ApiParam)({ name: 'type', description: '分享类型', example: 'default' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '分享配置获取成功',
        type: weixin_share_config_dto_1.WeixinShareConfigDto,
        schema: {
            example: {
                title: '一起来挑战词汇游戏！',
                path: '/pages/index/index',
                imageUrl: 'https://example.com/share.jpg',
                description: '挑战你的词汇量，看看你能通过多少关！',
                type: 'default',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '指定类型的分享配置不存在' }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getShareConfigByType", null);
__decorate([
    (0, common_1.Get)('vip-packages'),
    (0, swagger_1.ApiOperation)({ summary: '获取VIP套餐列表' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'VIP套餐列表获取成功',
        type: [payment_dto_1.VipPackageDto],
        schema: {
            example: [
                {
                    id: 'vip_monthly',
                    name: 'VIP月卡',
                    description: '30天VIP特权，无限制解锁关卡',
                    price: 2900,
                    duration: 30,
                    isActive: true,
                },
            ],
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getVipPackages", null);
__decorate([
    (0, common_1.Post)('create-payment'),
    (0, swagger_1.ApiOperation)({
        summary: '创建支付订单',
        description: `为指定用户和VIP套餐创建微信支付订单，返回小程序支付所需的参数。

订单配置：
- 订单有效期：30分钟（超时自动失效）
- 支持重复调用，不会产生重复订单

智能处理逻辑：
- 如果用户有相同套餐的未完成订单，直接返回该订单的支付参数
- 如果用户有不同套餐的未完成订单，自动取消旧订单并创建新订单
- 自动同步微信支付状态，确保订单状态准确性
- 订单过期后会自动失效，用户可重新创建订单`,
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '支付订单创建成功或返回现有订单支付参数',
        type: payment_dto_1.MiniProgramPaymentResponse,
        schema: {
            example: {
                appId: 'wx1234567890abcdef',
                timeStamp: '1640995200',
                nonceStr: 'abc123def456',
                package: 'prepay_id=wx123456789012345678901234567890',
                signType: 'RSA',
                paySign: 'signature_string_here',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误或创建订单失败' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在或VIP套餐不存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Get)('payment-orders'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户支付订单列表' }),
    (0, swagger_1.ApiQuery)({
        name: 'openid',
        description: '用户openid',
        example: 'wx_openid_123456',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '订单列表获取成功',
        schema: {
            example: [
                {
                    id: 'pay-order-12345678-1234',
                    out_trade_no: 'ORDER_1640995200_1234',
                    transaction_id: 'wx123456789012345678901234567890',
                    description: 'VIP月卡',
                    total: 2900,
                    status: 'SUCCESS',
                    vip_package_id: 'vip_monthly',
                    paid_at: '2023-12-01 12:00:00',
                    created_at: '2023-12-01 11:30:00',
                },
            ],
        },
    }),
    __param(0, (0, common_1.Query)('openid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getPaymentOrders", null);
__decorate([
    (0, common_1.Get)('payment-status/:out_trade_no'),
    (0, swagger_1.ApiOperation)({ summary: '查询支付订单状态' }),
    (0, swagger_1.ApiParam)({
        name: 'out_trade_no',
        description: '商户订单号',
        example: 'ORDER_1640995200_1234',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '订单查询成功',
        schema: {
            example: {
                id: 'pay-order-12345678-1234',
                out_trade_no: 'ORDER_1640995200_1234',
                transaction_id: 'wx123456789012345678901234567890',
                description: 'VIP月卡',
                total: 2900,
                status: 'SUCCESS',
                vip_package_id: 'vip_monthly',
                paid_at: '2023-12-01 12:00:00',
                created_at: '2023-12-01 11:30:00',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('out_trade_no')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "getPaymentStatus", null);
exports.WeixinController = WeixinController = __decorate([
    (0, swagger_1.ApiTags)('weixin'),
    (0, common_1.Controller)('weixin'),
    __metadata("design:paramtypes", [user_service_1.UserService,
        level_service_1.LevelService,
        weixin_api_service_1.WeixinApiService,
        share_service_1.ShareService,
        payment_service_1.PaymentService,
        settings_service_1.SettingsService])
], WeixinController);
//# sourceMappingURL=weixin.controller.js.map