import { ApiProperty } from '@nestjs/swagger';

export class PhraseResponseDto {
  @ApiProperty({ description: '词组的唯一ID', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
  id: string;

  @ApiProperty({ description: '词组文本', example: 'Hello World' })
  text: string;

  @ApiProperty({ description: '词组含义', example: '你好，世界' })
  meaning: string;

  @ApiProperty({
    description: '例句',
    example: 'When you start programming, the first thing you often do is print "Hello World".',
    required: false,
  })
  exampleSentence?: string;

  @ApiProperty({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] })
  tags?: string[];

  @ApiProperty({ description: '创建时间', example: '2023-10-27 10:30:00' })
  createdAt: string;

  @ApiProperty({ description: '最后更新时间', example: '2023-10-27 10:35:00' })
  updatedAt: string;
}