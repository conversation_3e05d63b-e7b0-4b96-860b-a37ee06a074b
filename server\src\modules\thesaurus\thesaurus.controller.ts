import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ThesaurusService } from './thesaurus.service';
import { CreateThesaurusDto } from './dto/create-thesaurus.dto';
import { UpdateThesaurusDto } from './dto/update-thesaurus.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { ThesaurusResponseDto } from './dto/thesaurus-response.dto';
import { AddPhraseToThesaurusDto } from './dto/add-phrase-to-thesaurus.dto';

@ApiTags('thesauruses')
@Controller('thesauruses')
export class ThesaurusController {
  constructor(private readonly thesaurusService: ThesaurusService) {}

  @Post()
  @ApiOperation({ summary: '创建新词库' })
  @ApiResponse({ status: 201, description: '词库创建成功', type: ThesaurusResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  create(@Body() createThesaurusDto: CreateThesaurusDto) {
    return this.thesaurusService.create(createThesaurusDto);
  }

  @Get()
  @ApiOperation({ summary: '获取所有词库列表' })
  @ApiResponse({ status: 200, description: '成功获取词库列表', type: [ThesaurusResponseDto] })
  findAll() {
    return this.thesaurusService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取单个词库' })
  @ApiParam({ name: 'id', description: '词库的UUID' })
  @ApiResponse({ status: 200, description: '成功获取词库', type: ThesaurusResponseDto })
  @ApiResponse({ status: 404, description: '词库未找到' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.thesaurusService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新指定ID的词库' })
  @ApiParam({ name: 'id', description: '词库的UUID' })
  @ApiResponse({ status: 200, description: '词库更新成功', type: ThesaurusResponseDto })
  @ApiResponse({ status: 404, description: '词库未找到' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  update(@Param('id', ParseUUIDPipe) id: string, @Body() updateThesaurusDto: UpdateThesaurusDto) {
    return this.thesaurusService.update(id, updateThesaurusDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除指定ID的词库' })
  @ApiParam({ name: 'id', description: '词库的UUID' })
  @ApiResponse({ status: 204, description: '词库删除成功' })
  @ApiResponse({ status: 404, description: '词库未找到' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.thesaurusService.remove(id);
  }

  // --- 词库与词组关联接口 ---

  @Post(':id/phrases')
  @ApiOperation({ summary: '向指定词库添加词组' })
  @ApiParam({ name: 'id', description: '词库的UUID' })
  @ApiResponse({ status: 200, description: '词组添加成功', type: ThesaurusResponseDto })
  @ApiResponse({ status: 404, description: '词库或词组未找到' })
  @ApiResponse({ status: 400, description: '请求参数错误或词组已存在' })
  addPhraseToThesaurus(
    @Param('id', ParseUUIDPipe) thesaurusId: string,
    @Body() addPhraseDto: AddPhraseToThesaurusDto,
  ) {
    return this.thesaurusService.addPhrase(thesaurusId, addPhraseDto.phraseId);
  }

  @Delete(':id/phrases/:phraseId')
  @ApiOperation({ summary: '从指定词库移除词组' })
  @ApiParam({ name: 'id', description: '词库的UUID' })
  @ApiParam({ name: 'phraseId', description: '词组的UUID' })
  @ApiResponse({ status: 200, description: '词组移除成功', type: ThesaurusResponseDto })
  @ApiResponse({ status: 404, description: '词库或词组未在词库中找到' })
  removePhraseFromThesaurus(
    @Param('id', ParseUUIDPipe) thesaurusId: string,
    @Param('phraseId', ParseUUIDPipe) phraseId: string,
  ) {
    return this.thesaurusService.removePhrase(thesaurusId, phraseId);
  }
}