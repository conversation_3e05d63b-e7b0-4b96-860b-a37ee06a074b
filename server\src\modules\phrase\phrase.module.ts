import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PhraseController } from './phrase.controller';
import { PhraseService } from './phrase.service';
import { Phrase, PhraseSchema } from './entities/phrase.entity';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Phrase.name, schema: PhraseSchema }])
  ],
  controllers: [PhraseController],
  providers: [PhraseService],
  exports: [PhraseService], // 确保 PhraseService 被导出
})
export class PhraseModule {}
