import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { CompleteLevelDto } from './dto/complete-level.dto';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    create(createUserDto: CreateUserDto): Promise<UserResponseDto>;
    findAll(search?: string, isVip?: boolean, startDate?: string, endDate?: string, page?: number, pageSize?: number): Promise<{
        users: UserResponseDto[];
        total: number;
    }>;
    findByOpenid(openid: string): Promise<UserResponseDto>;
    findByPhone(phone: string): Promise<UserResponseDto>;
    findOne(id: string): Promise<UserResponseDto>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto>;
    remove(id: string): Promise<void>;
    completeLevel(id: string, completeLevelDto: CompleteLevelDto): Promise<UserResponseDto>;
    startGame(id: string): Promise<UserResponseDto>;
    getUserStats(id: string): Promise<{
        totalGames: number;
        totalCompletions: number;
        unlockedLevels: number;
        completedLevels: number;
        completionRate: number;
    }>;
    resetUserProgress(id: string): Promise<UserResponseDto>;
}
