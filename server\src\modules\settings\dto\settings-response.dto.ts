import { ApiProperty } from '@nestjs/swagger';

export class SettingsResponseDto {
  @ApiProperty({ description: '设置的唯一ID' })
  id: string;

  @ApiProperty({ description: '设置键名' })
  key: string;

  @ApiProperty({ description: '设置值' })
  value: string;

  @ApiProperty({ description: '设置描述', required: false })
  description?: string;

  @ApiProperty({ description: '设置类型', enum: ['string', 'number', 'boolean', 'url'] })
  type: string;

  @ApiProperty({ description: '创建时间', example: '2024-01-01 12:00:00' })
  createdAt: string;

  @ApiProperty({ description: '最后更新时间', example: '2024-01-01 12:00:00' })
  updatedAt: string;
}
