/**
 * 数据库清理脚本：修复用户表中的空字符串phone字段
 * 将所有phone字段为空字符串""的记录更新为null
 */

const { MongoClient } = require('mongodb');

// 从环境变量获取数据库连接信息
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/xxl_dev_db';

async function fixEmptyPhoneStrings() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔗 连接到MongoDB...');
    await client.connect();
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    console.log('🔍 查找phone字段为空字符串的用户...');
    
    // 查找所有phone为空字符串的用户
    const usersWithEmptyPhone = await usersCollection.find({ phone: "" }).toArray();
    console.log(`📊 找到 ${usersWithEmptyPhone.length} 个用户的phone字段为空字符串`);
    
    if (usersWithEmptyPhone.length > 0) {
      console.log('🔧 开始修复...');
      
      // 将空字符串更新为null
      const result = await usersCollection.updateMany(
        { phone: "" },
        { $unset: { phone: "" } }  // 使用$unset删除字段，这样字段就变成undefined/null
      );
      
      console.log(`✅ 成功修复 ${result.modifiedCount} 个用户记录`);
      
      // 验证修复结果
      const remainingEmptyPhone = await usersCollection.countDocuments({ phone: "" });
      console.log(`🔍 验证：还有 ${remainingEmptyPhone} 个用户的phone字段为空字符串`);
      
      if (remainingEmptyPhone === 0) {
        console.log('🎉 所有空字符串phone字段已成功修复！');
      } else {
        console.log('⚠️  仍有部分记录未修复，请检查');
      }
    } else {
      console.log('✅ 没有发现需要修复的记录');
    }
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('🔚 数据库连接已关闭');
  }
}

// 运行脚本
if (require.main === module) {
  fixEmptyPhoneStrings()
    .then(() => {
      console.log('🏁 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { fixEmptyPhoneStrings };
