{"openapi": "3.0.0", "paths": {"/api/v1/levels": {"post": {"operationId": "LevelController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLevelDto"}}}}, "responses": {"200": {"description": "关卡创建成功（兼容旧版，实际应为201）", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LevelResponseDto"}}}}, "201": {"description": "关卡创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LevelResponseDto"}}}}, "400": {"description": "请求参数错误"}}, "summary": "创建新关卡", "tags": ["levels"]}, "get": {"operationId": "LevelController_findAll", "parameters": [], "responses": {"200": {"description": "成功获取关卡列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LevelResponseDto"}}}}}}, "summary": "获取所有关卡列表", "tags": ["levels"]}}, "/api/v1/levels/count": {"get": {"operationId": "LevelController_getLevelCount", "parameters": [], "responses": {"200": {"description": "成功获取关卡总数", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "number", "description": "关卡总数"}, "maxLevels": {"type": "number", "description": "最大关卡数限制"}, "remaining": {"type": "number", "description": "剩余可创建关卡数"}}}}}}}, "summary": "获取关卡总数", "tags": ["levels"]}}, "/api/v1/levels/difficulty/{difficulty}": {"get": {"operationId": "LevelController_findByDifficulty", "parameters": [{"name": "difficulty", "required": true, "in": "path", "description": "关卡难度 (1-5)", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取指定难度的关卡列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LevelResponseDto"}}}}}, "400": {"description": "难度参数无效"}}, "summary": "根据难度获取关卡列表", "tags": ["levels"]}}, "/api/v1/levels/{id}": {"get": {"operationId": "LevelController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "关卡的UUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取关卡", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LevelResponseDto"}}}}, "404": {"description": "关卡未找到"}}, "summary": "根据ID获取单个关卡", "tags": ["levels"]}, "patch": {"operationId": "LevelController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "关卡的UUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLevelDto"}}}}, "responses": {"200": {"description": "关卡更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LevelResponseDto"}}}}, "400": {"description": "请求参数错误"}, "404": {"description": "关卡未找到"}}, "summary": "更新指定ID的关卡", "tags": ["levels"]}, "delete": {"operationId": "LevelController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "关卡的UUID", "schema": {"type": "string"}}], "responses": {"204": {"description": "关卡删除成功"}, "404": {"description": "关卡未找到"}}, "summary": "删除指定ID的关卡", "tags": ["levels"]}}, "/api/v1/levels/{id}/with-phrases": {"get": {"operationId": "LevelController_findOneWithPhrases", "parameters": [{"name": "id", "required": true, "in": "path", "description": "关卡的UUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取关卡详细信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "difficulty": {"type": "number"}, "description": {"type": "string"}, "thesaurusIds": {"type": "array", "items": {"type": "string"}}, "phraseIds": {"type": "array", "items": {"type": "string"}}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "phrases": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "text": {"type": "string"}, "meaning": {"type": "string"}, "example": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "404": {"description": "关卡未找到"}}, "summary": "获取关卡详细信息（包含词组详情）", "tags": ["levels"]}}, "/api/v1/levels/{id}/phrases": {"post": {"operationId": "LevelController_addPhraseToLevel", "parameters": [{"name": "id", "required": true, "in": "path", "description": "关卡的UUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddPhraseToLevelDto"}}}}, "responses": {"200": {"description": "词组添加成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LevelResponseDto"}}}}, "400": {"description": "请求参数错误、词组不属于关联词库或词组已存在于关卡中"}, "404": {"description": "关卡或词组未找到"}}, "summary": "向指定关卡添加词组", "tags": ["levels"]}}, "/api/v1/levels/{id}/phrases/{phraseId}": {"delete": {"operationId": "LevelController_removePhraseFromLevel", "parameters": [{"name": "id", "required": true, "in": "path", "description": "关卡的UUID", "schema": {"type": "string"}}, {"name": "phraseId", "required": true, "in": "path", "description": "词组的UUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "词组移除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LevelResponseDto"}}}}, "404": {"description": "关卡或词组未在关卡中找到"}}, "summary": "从指定关卡移除词组", "tags": ["levels"]}}, "/api/v1/thesauruses": {"post": {"operationId": "ThesaurusController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateThesaurusDto"}}}}, "responses": {"201": {"description": "词库创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThesaurusResponseDto"}}}}, "400": {"description": "请求参数错误"}}, "summary": "创建新词库", "tags": ["thesauruses"]}, "get": {"operationId": "ThesaurusController_findAll", "parameters": [], "responses": {"200": {"description": "成功获取词库列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ThesaurusResponseDto"}}}}}}, "summary": "获取所有词库列表", "tags": ["thesauruses"]}}, "/api/v1/thesauruses/{id}": {"get": {"operationId": "ThesaurusController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "词库的UUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取词库", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThesaurusResponseDto"}}}}, "404": {"description": "词库未找到"}}, "summary": "根据ID获取单个词库", "tags": ["thesauruses"]}, "patch": {"operationId": "ThesaurusController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "词库的UUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateThesaurusDto"}}}}, "responses": {"200": {"description": "词库更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThesaurusResponseDto"}}}}, "400": {"description": "请求参数错误"}, "404": {"description": "词库未找到"}}, "summary": "更新指定ID的词库", "tags": ["thesauruses"]}, "delete": {"operationId": "ThesaurusController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "词库的UUID", "schema": {"type": "string"}}], "responses": {"204": {"description": "词库删除成功"}, "404": {"description": "词库未找到"}}, "summary": "删除指定ID的词库", "tags": ["thesauruses"]}}, "/api/v1/thesauruses/{id}/phrases": {"post": {"operationId": "ThesaurusController_addPhraseToThesaurus", "parameters": [{"name": "id", "required": true, "in": "path", "description": "词库的UUID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddPhraseToThesaurusDto"}}}}, "responses": {"200": {"description": "词组添加成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThesaurusResponseDto"}}}}, "400": {"description": "请求参数错误或词组已存在"}, "404": {"description": "词库或词组未找到"}}, "summary": "向指定词库添加词组", "tags": ["thesauruses"]}}, "/api/v1/thesauruses/{id}/phrases/{phraseId}": {"delete": {"operationId": "ThesaurusController_removePhraseFromThesaurus", "parameters": [{"name": "id", "required": true, "in": "path", "description": "词库的UUID", "schema": {"type": "string"}}, {"name": "phraseId", "required": true, "in": "path", "description": "词组的UUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "词组移除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThesaurusResponseDto"}}}}, "404": {"description": "词库或词组未在词库中找到"}}, "summary": "从指定词库移除词组", "tags": ["thesauruses"]}}, "/api/v1/phrases": {"post": {"operationId": "PhraseController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePhraseDto"}}}}, "responses": {"201": {"description": "词组创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhraseResponseDto"}}}}, "400": {"description": "请求参数错误"}}, "summary": "创建新词组", "tags": ["phrases"]}, "get": {"operationId": "PhraseController_findAll", "parameters": [], "responses": {"200": {"description": "成功获取词组列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PhraseResponseDto"}}}}}}, "summary": "获取所有词组列表", "tags": ["phrases"]}}, "/api/v1/phrases/{id}": {"get": {"operationId": "PhraseController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取词组", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhraseResponseDto"}}}}, "404": {"description": "词组未找到"}}, "summary": "根据ID获取单个词组", "tags": ["phrases"]}, "patch": {"operationId": "PhraseController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePhraseDto"}}}}, "responses": {"200": {"description": "词组更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhraseResponseDto"}}}}, "400": {"description": "请求参数错误"}, "404": {"description": "词组未找到"}}, "summary": "更新指定ID的词组", "tags": ["phrases"]}, "delete": {"operationId": "PhraseController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": "词组删除成功"}, "404": {"description": "词组未找到"}}, "summary": "删除指定ID的词组", "tags": ["phrases"]}}, "/api/v1/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginAdminDto"}}}}, "responses": {"200": {"description": "登录成功，返回 accessToken"}, "400": {"description": "请求参数错误"}, "401": {"description": "用户名或密码错误"}}, "summary": "管理员登录", "tags": ["auth"]}}, "/api/v1/users": {"post": {"operationId": "UserController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "用户创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "400": {"description": "openid已存在或请求参数无效"}}, "summary": "创建新用户", "tags": ["users"]}, "get": {"description": "支持搜索、VIP状态过滤、日期范围过滤和分页", "operationId": "UserController_findAll", "parameters": [{"name": "search", "required": false, "in": "query", "description": "搜索关键词（手机号、昵称、ID）", "schema": {"type": "string"}}, {"name": "isVip", "required": false, "in": "query", "description": "VIP状态过滤", "schema": {"type": "boolean"}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期 (YYYY-MM-DD)", "schema": {"type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期 (YYYY-MM-DD)", "schema": {"type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "页码，默认1", "schema": {"type": "number"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量，默认20", "schema": {"type": "number"}}], "responses": {"200": {"description": "用户列表获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponseDto"}}, "total": {"type": "number", "description": "总用户数"}}}}}}}, "summary": "获取用户列表", "tags": ["users"]}}, "/api/v1/users/by-openid": {"get": {"operationId": "UserController_findByOpenid", "parameters": [{"name": "openid", "required": true, "in": "query", "description": "微信用户openid", "schema": {"type": "string"}}], "responses": {"200": {"description": "用户信息获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "根据openid获取用户信息", "tags": ["users"]}}, "/api/v1/users/by-phone": {"get": {"operationId": "UserController_findByPhone", "parameters": [{"name": "phone", "required": true, "in": "query", "description": "用户手机号", "schema": {"type": "string"}}], "responses": {"200": {"description": "用户信息获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "根据手机号获取用户信息", "tags": ["users"]}}, "/api/v1/users/{id}": {"get": {"operationId": "UserController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "用户信息获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "根据ID获取用户信息", "tags": ["users"]}, "patch": {"operationId": "UserController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "用户信息更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "更新用户信息", "tags": ["users"]}, "delete": {"operationId": "UserController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "string"}}], "responses": {"204": {"description": "用户删除成功"}, "404": {"description": "用户不存在"}}, "summary": "删除用户", "tags": ["users"]}}, "/api/v1/users/{id}/complete-level": {"post": {"operationId": "UserController_completeLevel", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteLevelDto"}}}}, "responses": {"200": {"description": "关卡完成记录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "400": {"description": "关卡已完成或关卡不存在"}, "404": {"description": "用户不存在"}}, "summary": "用户完成关卡", "tags": ["users"]}}, "/api/v1/users/{id}/start-game": {"post": {"operationId": "UserController_startGame", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "游戏开始记录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "用户开始游戏", "tags": ["users"]}}, "/api/v1/users/{id}/stats": {"get": {"operationId": "UserController_getUserStats", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "用户统计获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalGames": {"type": "number", "description": "总游戏次数"}, "totalCompletions": {"type": "number", "description": "总通关次数"}, "unlockedLevels": {"type": "number", "description": "已解锁关卡数"}, "completedLevels": {"type": "number", "description": "已完成关卡数"}, "completionRate": {"type": "number", "description": "通关率(%)"}}}}}}, "404": {"description": "用户不存在"}}, "summary": "获取用户游戏统计", "tags": ["users"]}}, "/api/v1/users/{id}/reset-progress": {"post": {"operationId": "UserController_resetUserProgress", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "用户进度重置成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "重置用户游戏进度", "tags": ["users"]}}, "/api/v1/share": {"post": {"operationId": "ShareController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShareConfigDto"}}}}, "responses": {"201": {"description": "分享配置创建成功", "schema": {"example": {"id": "share-config-001", "name": "默认分享配置", "title": "一起来挑战词汇游戏！", "path": "/pages/index/index", "imageUrl": "https://example.com/share.jpg", "description": "挑战你的词汇量，看看你能通过多少关！", "type": "default", "isActive": true, "sortOrder": 1, "createdAt": "2025-06-19T10:00:00.000Z", "updatedAt": "2025-06-19T10:00:00.000Z"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}, "400": {"description": "参数无效或默认配置已存在"}}, "summary": "创建分享配置", "tags": ["分享管理"]}, "get": {"operationId": "ShareController_findAll", "parameters": [], "responses": {"200": {"description": "分享配置列表获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}}}, "summary": "获取所有分享配置", "tags": ["分享管理"]}}, "/api/v1/share/active": {"get": {"operationId": "ShareController_findActive", "parameters": [], "responses": {"200": {"description": "启用的分享配置列表获取成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}}}, "summary": "获取启用的分享配置", "tags": ["分享管理"]}}, "/api/v1/share/default": {"get": {"operationId": "ShareController_getDefault", "parameters": [], "responses": {"200": {"description": "默认分享配置获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}, "404": {"description": "默认分享配置不存在"}}, "summary": "获取默认分享配置", "tags": ["分享管理"]}}, "/api/v1/share/type/{type}": {"get": {"operationId": "ShareController_findByType", "parameters": [{"name": "type", "required": true, "in": "path", "description": "分享类型", "schema": {"example": "default", "type": "string"}}], "responses": {"200": {"description": "分享配置获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}, "404": {"description": "指定类型的分享配置不存在"}}, "summary": "根据类型获取分享配置", "tags": ["分享管理"]}}, "/api/v1/share/{id}": {"get": {"operationId": "ShareController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "分享配置ID", "schema": {"example": "share-config-001", "type": "string"}}], "responses": {"200": {"description": "分享配置获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}, "404": {"description": "分享配置不存在"}}, "summary": "根据ID获取分享配置", "tags": ["分享管理"]}, "patch": {"operationId": "ShareController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "分享配置ID", "schema": {"example": "share-config-001", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateShareConfigDto"}}}}, "responses": {"200": {"description": "分享配置更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}, "400": {"description": "参数无效"}, "404": {"description": "分享配置不存在"}}, "summary": "更新分享配置", "tags": ["分享管理"]}, "delete": {"operationId": "ShareController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "分享配置ID", "schema": {"example": "share-config-001", "type": "string"}}], "responses": {"200": {"description": "分享配置删除成功"}, "400": {"description": "不能删除默认配置"}, "404": {"description": "分享配置不存在"}}, "summary": "删除分享配置", "tags": ["分享管理"]}}, "/api/v1/share/{id}/toggle": {"put": {"operationId": "ShareController_toggleActive", "parameters": [{"name": "id", "required": true, "in": "path", "description": "分享配置ID", "schema": {"example": "share-config-001", "type": "string"}}], "responses": {"200": {"description": "分享配置状态切换成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareConfigResponseDto"}}}}, "404": {"description": "分享配置不存在"}}, "summary": "启用/禁用分享配置", "tags": ["分享管理"]}}, "/api/v1/payment/config/wechat": {"get": {"description": "检查微信支付相关配置是否正确，用于调试支付问题", "operationId": "PaymentController_getWechatPayConfig", "parameters": [], "responses": {"200": {"description": "微信支付配置状态", "content": {"application/json": {"schema": {"example": {"appId": "wx280e45091c5ef854", "mchId": "1619236596", "hasApiKey": true, "hasSerialNo": true, "hasNotifyUrl": true, "hasPrivateKey": true, "notifyUrl": "https://localhost:3001/api/v1/payment/notify", "isConfigured": true, "timestamp": "2025-06-21T12:30:00.000Z"}}}}}}, "summary": "获取微信支付配置状态（调试用）", "tags": ["支付管理"]}}, "/api/v1/payment/vip-packages": {"get": {"operationId": "PaymentController_getVipPackages", "parameters": [], "responses": {"200": {"description": "VIP套餐列表获取成功", "schema": {"example": [{"id": "vip_monthly", "name": "VIP月卡", "description": "30天VIP特权，无限制解锁关卡", "price": 2900, "duration": 30, "sortOrder": 1, "isActive": true, "createdAt": "2023-12-01 10:00:00", "updatedAt": "2023-12-01 10:00:00"}]}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VipPackageDto"}}}}}}, "summary": "获取VIP套餐列表", "tags": ["支付管理"]}, "post": {"operationId": "PaymentController_createVipPackage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVipPackageDto"}}}}, "responses": {"201": {"description": "VIP套餐创建成功，ID自动生成", "schema": {"example": {"id": "vip_monthly_30d_a1b2", "name": "VIP月卡", "description": "30天VIP特权，无限制解锁关卡", "price": 2900, "duration": 30, "sortOrder": 1, "isActive": true, "createdAt": "2023-12-01 10:00:00", "updatedAt": "2023-12-01 10:00:00"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipPackageDto"}}}}, "400": {"description": "参数错误"}}, "summary": "创建VIP套餐（ID自动生成）", "tags": ["支付管理"]}}, "/api/v1/payment/vip-packages/{id}": {"get": {"operationId": "PaymentController_getVipPackageById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "VIP套餐ID", "schema": {"example": "vip_monthly", "type": "string"}}], "responses": {"200": {"description": "VIP套餐详情获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipPackageDto"}}}}, "404": {"description": "VIP套餐不存在"}}, "summary": "根据ID获取VIP套餐详情", "tags": ["支付管理"]}, "put": {"operationId": "PaymentController_updateVipPackage", "parameters": [{"name": "id", "required": true, "in": "path", "description": "VIP套餐ID", "schema": {"example": "vip_monthly", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVipPackageDto"}}}}, "responses": {"200": {"description": "VIP套餐更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VipPackageDto"}}}}, "400": {"description": "参数错误"}, "404": {"description": "VIP套餐不存在"}}, "summary": "更新VIP套餐", "tags": ["支付管理"]}, "delete": {"operationId": "PaymentController_deleteVipPackage", "parameters": [{"name": "id", "required": true, "in": "path", "description": "VIP套餐ID", "schema": {"example": "vip_monthly", "type": "string"}}], "responses": {"200": {"description": "VIP套餐删除成功"}, "400": {"description": "存在相关订单，无法删除"}, "404": {"description": "VIP套餐不存在"}}, "summary": "删除VIP套餐", "tags": ["支付管理"]}}, "/api/v1/payment/create-order": {"post": {"operationId": "PaymentController_createPaymentOrder", "parameters": [], "responses": {"201": {"description": "支付订单创建成功，返回小程序支付参数", "schema": {"example": {"appId": "wx1234567890abcdef", "timeStamp": "1640995200", "nonceStr": "abc123def456", "package": "prepay_id=wx123456789012345678901234567890", "signType": "RSA", "paySign": "signature_string_here"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniProgramPaymentResponse"}}}}, "400": {"description": "参数错误或创建订单失败"}, "404": {"description": "用户不存在或VIP套餐不存在"}}, "summary": "创建支付订单", "tags": ["支付管理"]}}, "/api/v1/payment/notify": {"post": {"operationId": "PaymentController_handlePaymentNotify", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentNotifyDto"}}}}, "responses": {"200": {"description": "回调处理成功"}, "400": {"description": "回调处理失败"}}, "summary": "微信支付回调通知", "tags": ["支付管理"]}}, "/api/v1/payment/query/{out_trade_no}": {"get": {"operationId": "PaymentController_queryPaymentOrder", "parameters": [{"name": "out_trade_no", "required": true, "in": "path", "description": "商户订单号", "schema": {"example": "ORDER_1640995200_1234", "type": "string"}}], "responses": {"200": {"description": "订单查询成功", "content": {"application/json": {"schema": {"example": {"id": "pay-order-12345678-1234", "out_trade_no": "ORDER_1640995200_1234", "transaction_id": "wx123456789012345678901234567890", "description": "VIP月卡", "total": 2900, "status": "SUCCESS", "vip_package_id": "vip_monthly", "paid_at": "2023-12-01 12:00:00", "created_at": "2023-12-01 11:30:00"}}}}}, "404": {"description": "订单不存在"}}, "summary": "查询支付订单状态", "tags": ["支付管理"]}}, "/api/v1/payment/refresh/{out_trade_no}": {"post": {"description": "主动查询微信支付状态并同步到本地订单，用于调试支付状态同步问题", "operationId": "PaymentController_refreshPaymentOrderStatus", "parameters": [{"name": "out_trade_no", "required": true, "in": "path", "description": "商户订单号", "schema": {"example": "ORDER_1640995200_1234", "type": "string"}}], "responses": {"200": {"description": "状态刷新成功", "content": {"application/json": {"schema": {"example": {"message": "订单状态已刷新", "localStatus": "SUCCESS", "wechatStatus": "SUCCESS", "synced": true}}}}}, "404": {"description": "订单不存在"}}, "summary": "手动刷新支付订单状态", "tags": ["支付管理"]}}, "/api/v1/payment/orders": {"get": {"operationId": "PaymentController_getPaymentOrders", "parameters": [{"name": "page", "required": false, "in": "query", "description": "页码", "schema": {"example": 1, "type": "string"}}, {"name": "pageSize", "required": false, "in": "query", "description": "每页数量", "schema": {"example": 20, "type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "搜索关键词（订单号、用户ID、描述）", "schema": {"type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "订单状态", "schema": {"enum": ["PENDING", "SUCCESS", "FAILED", "CANCELLED", "REFUNDED"], "type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "开始日期", "schema": {"example": "2023-12-01", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期", "schema": {"example": "2023-12-31", "type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "用户ID（获取特定用户订单）", "schema": {"type": "string"}}], "responses": {"200": {"description": "订单列表获取成功", "content": {"application/json": {"schema": {"example": {"orders": [{"id": "pay-order-12345678-1234", "userId": "12345678", "openid": "wx_openid_123456", "out_trade_no": "ORDER_1640995200_1234", "transaction_id": "wx123456789012345678901234567890", "description": "VIP月卡", "total": 2900, "status": "SUCCESS", "vip_package_id": "vip_monthly", "paid_at": "2023-12-01 12:00:00", "expires_at": "2023-12-01 12:30:00", "created_at": "2023-12-01 11:30:00", "updated_at": "2023-12-01 12:00:00"}], "total": 1, "page": 1, "pageSize": 20, "totalPages": 1}}}}}}, "summary": "获取支付订单列表（管理员）", "tags": ["支付管理"]}}, "/api/v1/payment/orders/user/{userId}": {"get": {"operationId": "PaymentController_getUserPaymentOrders", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "用户ID", "schema": {"example": "12345678", "type": "string"}}], "responses": {"200": {"description": "订单列表获取成功", "content": {"application/json": {"schema": {"example": [{"id": "pay-order-12345678-1234", "out_trade_no": "ORDER_1640995200_1234", "transaction_id": "wx123456789012345678901234567890", "description": "VIP月卡", "total": 2900, "status": "SUCCESS", "vip_package_id": "vip_monthly", "paid_at": "2023-12-01 12:00:00", "created_at": "2023-12-01 11:30:00"}]}}}}}, "summary": "获取用户支付订单列表", "tags": ["支付管理"]}}, "/api/v1/payment/orders/stats": {"get": {"operationId": "PaymentController_getPaymentOrderStats", "parameters": [{"name": "startDate", "required": false, "in": "query", "description": "开始日期", "schema": {"example": "2023-12-01", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "结束日期", "schema": {"example": "2023-12-31", "type": "string"}}], "responses": {"200": {"description": "统计信息获取成功", "content": {"application/json": {"schema": {"example": {"total": 100, "pending": 10, "success": 80, "failed": 8, "cancelled": 2, "refunded": 0, "totalAmount": 290000, "successAmount": 232000, "pendingAmount": 29000, "successRate": 80, "avgOrderAmount": 2900}}}}}}, "summary": "获取支付订单统计信息", "tags": ["支付管理"]}}, "/api/v1/payment/orders/{id}": {"get": {"operationId": "PaymentController_getPaymentOrderById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "订单ID", "schema": {"example": "pay-order-12345678-1234", "type": "string"}}], "responses": {"200": {"description": "订单详情获取成功", "content": {"application/json": {"schema": {"example": {"id": "pay-order-12345678-1234", "userId": "12345678", "openid": "wx_openid_123456", "out_trade_no": "ORDER_1640995200_1234", "transaction_id": "wx123456789012345678901234567890", "description": "VIP月卡", "total": 2900, "status": "SUCCESS", "vip_package_id": "vip_monthly", "prepay_id": "wx201410272009395522657a690389285100", "detail": "VIP月卡详情", "attach": "{\"userId\":\"12345678\",\"packageId\":\"vip_monthly\"}", "paid_at": "2023-12-01 12:00:00", "expires_at": "2023-12-01 12:30:00", "created_at": "2023-12-01 11:30:00", "updated_at": "2023-12-01 12:00:00"}}}}}, "404": {"description": "订单不存在"}}, "summary": "根据ID获取支付订单详情", "tags": ["支付管理"]}}, "/api/v1/payment/cancel/{out_trade_no}": {"post": {"operationId": "PaymentController_cancelPaymentOrder", "parameters": [{"name": "out_trade_no", "required": true, "in": "path", "description": "商户订单号", "schema": {"example": "ORDER_1640995200_1234", "type": "string"}}], "responses": {"200": {"description": "订单取消成功"}, "404": {"description": "订单不存在或无法取消"}}, "summary": "取消支付订单", "tags": ["支付管理"]}}, "/api/v1/weixin/login": {"post": {"operationId": "WeixinController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinLoginDto"}}}}, "responses": {"201": {"description": "登录成功或需要绑定手机号", "schema": {"examples": {"loginSuccess": {"summary": "登录成功", "value": {"status": "success", "message": "登录成功", "openid": "oGZUI0egBJY1zhBYw2KhdUfwVJJE", "userInfo": {"id": "12345678", "maskedPhone": "138****8000", "nickname": "微信用户", "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/xxx", "unlockedLevels": 5, "completedLevelIds": ["1", "2", "3"], "totalGames": 20, "totalCompletions": 15, "lastPlayTime": "2025-06-18T12:00:00.000Z", "createdAt": "2025-06-18T10:00:00.000Z"}}}, "needBind": {"summary": "需要绑定手机号", "value": {"status": "need_bind", "message": "请绑定手机号完成注册", "openid": "oGZUI0egBJY1zhBYw2KhdUfwVJJE"}}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinLoginResponseDto"}}}}, "400": {"description": "登录凭证无效或微信接口调用失败", "content": {"application/json": {"schema": {"examples": {"invalidCode": {"summary": "登录凭证无效", "value": {"statusCode": 400, "message": "微信登录失败: 登录凭证无效或已过期，请重新登录 (错误码: 40029)", "error": "Bad Request"}}, "configError": {"summary": "配置错误", "value": {"statusCode": 400, "message": "微信登录失败: AppID无效，请检查小程序配置 (错误码: 40013)", "error": "Bad Request"}}}}}}}}, "summary": "微信小程序登录", "tags": ["weixin"]}}, "/api/v1/weixin/bind-phone": {"post": {"operationId": "WeixinController_bindPhone", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinPhoneBindDto"}}}}, "responses": {"201": {"description": "绑定成功", "schema": {"example": {"status": "success", "message": "绑定成功", "openid": "oGZUI0egBJY1zhBYw2KhdUfwVJJE", "userInfo": {"id": "12345678", "maskedPhone": "138****8000", "nickname": "微信用户", "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/xxx", "unlockedLevels": 1, "completedLevelIds": [], "totalGames": 0, "totalCompletions": 0, "lastPlayTime": "2025-06-18T12:00:00.000Z", "createdAt": "2025-06-18T12:00:00.000Z"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinLoginResponseDto"}}}}, "400": {"description": "手机号已被使用或openid已绑定用户"}}, "summary": "微信用户绑定手机号", "tags": ["weixin"]}}, "/api/v1/weixin/config": {"get": {"operationId": "WeixinController_getConfig", "parameters": [], "responses": {"200": {"description": "配置状态", "content": {"application/json": {"schema": {"example": {"appId": "wx1234567890abcdef...", "appSecret": "已配置", "isConfigured": true, "paymentOrderExpireMinutes": 30, "timestamp": "2025-06-21T10:30:00.000Z", "environment": "development"}}}}}}, "summary": "获取微信小程序配置状态（调试用）", "tags": ["weixin"]}}, "/api/v1/weixin/global-config": {"get": {"operationId": "WeixinController_getGlobalConfig", "parameters": [], "responses": {"200": {"description": "全局配置获取成功", "schema": {"example": {"appId": "wx1234567890abcdef", "isConfigured": true, "paymentOrderExpireMinutes": 30, "environment": "development", "timestamp": "2024-01-01T12:00:00.000Z", "helpUrl": "https://help.example.com", "backgroundMusicUrl": "https://music.example.com/background.mp3", "version": "1.0.0", "features": {"enablePayment": true, "enableShare": true, "enableVip": true, "enableMusic": true}, "gameConfig": {"maxLevels": 1000, "dailyUnlockLimit": 15, "shareRewardCount": 5}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinGlobalConfigDto"}}}}, "500": {"description": "获取配置失败"}}, "summary": "获取微信小程序全局配置", "tags": ["weixin"]}}, "/api/v1/weixin/app-settings": {"get": {"operationId": "WeixinController_getAppSettings", "parameters": [], "responses": {"200": {"description": "应用设置获取成功", "schema": {"example": {"helpUrl": "https://help.example.com", "backgroundMusicUrl": "https://music.example.com/background.mp3"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinAppSettingsDto"}}}}, "500": {"description": "获取设置失败"}}, "summary": "获取小程序应用设置", "tags": ["weixin"]}}, "/api/v1/weixin/user/bind": {"post": {"operationId": "WeixinController_bindUser", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinUserBindDto"}}}}, "responses": {"201": {"description": "用户绑定成功", "schema": {"example": {"id": "12345678", "maskedPhone": "138****8000", "nickname": "微信用户", "avatarUrl": "https://wx.qlogo.cn/mmopen/...", "unlockedLevels": 1, "completedLevelIds": [], "totalGames": 0, "totalCompletions": 0, "lastPlayTime": "2025-06-18T12:00:00.000Z", "createdAt": "2025-06-18T12:00:00.000Z"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinUserInfoDto"}}}}, "400": {"description": "手机号或openid已存在，或参数无效"}}, "summary": "微信小程序用户绑定", "tags": ["weixin"]}}, "/api/v1/weixin/user/info": {"get": {"operationId": "WeixinController_getUserInfo", "parameters": [{"name": "openid", "required": true, "in": "query", "description": "微信用户openid", "schema": {"example": "wx_openid_123456", "type": "string"}}], "responses": {"200": {"description": "用户信息获取成功", "schema": {"example": {"id": "12345678", "maskedPhone": "138****8000", "nickname": "微信用户", "avatarUrl": "https://wx.qlogo.cn/mmopen/...", "unlockedLevels": 5, "completedLevelIds": ["1", "2", "3"], "totalGames": 20, "totalCompletions": 15, "lastPlayTime": "2025-06-18T12:00:00.000Z", "createdAt": "2025-06-18T10:00:00.000Z"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinUserInfoDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "根据openid获取微信小程序用户信息", "tags": ["weixin"]}}, "/api/v1/weixin/levels": {"get": {"operationId": "WeixinController_getLevelsWithProgress", "parameters": [{"name": "openid", "required": true, "in": "query", "description": "微信用户openid", "schema": {"example": "wx_openid_123456", "type": "string"}}], "responses": {"200": {"description": "关卡列表获取成功", "schema": {"example": [{"id": "level-uuid-1", "name": "第1关 - 基础词汇", "difficulty": 1, "description": "这是第一关，包含基础词汇", "isUnlocked": true, "isCompleted": true, "createdAt": "2025-06-18T10:00:00.000Z"}, {"id": "level-uuid-2", "name": "第2关 - 进阶词汇", "difficulty": 2, "description": "这是第二关，包含进阶词汇", "isUnlocked": true, "isCompleted": false, "createdAt": "2025-06-18T10:00:00.000Z"}, {"id": "level-uuid-3", "name": "第3关 - 高级词汇", "difficulty": 3, "description": "这是第三关，包含高级词汇", "isUnlocked": false, "isCompleted": false, "createdAt": "2025-06-18T10:00:00.000Z"}]}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeixinLevelDto"}}}}}, "404": {"description": "用户不存在"}}, "summary": "获取微信小程序关卡列表（包含用户进度）", "tags": ["weixin"]}}, "/api/v1/weixin/user/{id}/info": {"get": {"operationId": "WeixinController_getUserInfoById", "parameters": [{"name": "id", "required": true, "in": "path", "description": "用户ID", "schema": {"example": "12345678", "type": "string"}}], "responses": {"200": {"description": "用户信息获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinUserInfoDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "根据用户ID获取微信小程序用户信息", "tags": ["weixin"]}}, "/api/v1/weixin/level/{levelId}": {"get": {"operationId": "WeixinController_getLevelDetail", "parameters": [{"name": "levelId", "required": true, "in": "path", "description": "关卡ID", "schema": {"example": "level-uuid-1", "type": "string"}}, {"name": "openid", "required": true, "in": "query", "description": "微信用户openid", "schema": {"example": "wx_openid_123456", "type": "string"}}], "responses": {"200": {"description": "关卡详情获取成功", "schema": {"example": {"id": "level-uuid-1", "name": "第1关 - 基础词汇", "difficulty": 1, "description": "这是第一关，包含基础词汇", "isUnlocked": true, "isCompleted": false, "phrases": [{"id": "phrase-uuid-1", "text": "Hello World", "meaning": "你好，世界", "exampleSentence": "When you start programming, the first thing you often do is print \"Hello World\".", "tags": ["greeting", "common"]}, {"id": "phrase-uuid-2", "text": "Good Morning", "meaning": "早上好", "exampleSentence": "Good morning! How are you today?", "tags": ["greeting", "daily"]}], "createdAt": "2025-06-18T10:00:00.000Z"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinLevelDetailDto"}}}}, "400": {"description": "关卡尚未解锁"}, "404": {"description": "用户不存在或关卡不存在"}}, "summary": "根据关卡ID获取微信小程序关卡详情（包含词组信息）", "tags": ["weixin"]}}, "/api/v1/weixin/level/complete": {"post": {"operationId": "WeixinController_completeLevel", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinCompleteLevelDto"}}}}, "responses": {"201": {"description": "关卡通关成功", "schema": {"example": {"message": "恭喜！关卡通关成功，解锁新关卡！", "userId": "12345678", "levelId": "level-uuid-1", "unlockedLevels": 2, "totalCompletions": 1, "hasUnlockedNewLevel": true}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinCompleteLevelResponseDto"}}}}, "400": {"description": "关卡已完成、尚未解锁或参数无效"}, "404": {"description": "用户不存在或关卡不存在"}}, "summary": "微信小程序用户通关关卡", "tags": ["weixin"]}}, "/api/v1/weixin/daily-status": {"get": {"operationId": "WeixinController_getDailyStatus", "parameters": [{"name": "openid", "required": true, "in": "query", "description": "微信用户openid", "schema": {"example": "wx_openid_123456", "type": "string"}}], "responses": {"200": {"description": "每日状态获取成功", "schema": {"example": {"id": "12345678", "dailyPlayCount": 3, "dailyPlayLimit": 15, "remainingPlays": 12, "dailyShared": false, "isVip": false, "lastPlayDate": "2025-06-19", "totalShares": 5, "canPlay": true}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinDailyStatusDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "获取用户每日通关状态", "tags": ["weixin"]}}, "/api/v1/weixin/share": {"post": {"operationId": "WeixinController_share", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinShareDto"}}}}, "responses": {"201": {"description": "分享成功或已分享", "schema": {"examples": {"shareSuccess": {"summary": "分享成功", "value": {"status": "success", "message": "分享成功，获得5次额外通关机会！", "userId": "12345678", "dailyPlayCount": 3, "dailyPlayLimit": 20, "remainingPlays": 17, "isVip": false, "totalShares": 6}}, "alreadyShared": {"summary": "今日已分享", "value": {"status": "already_shared", "message": "今日已分享过，无法重复获得奖励", "userId": "12345678", "dailyPlayCount": 3, "dailyPlayLimit": 15, "remainingPlays": 12, "isVip": false, "totalShares": 5}}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinShareResponseDto"}}}}, "404": {"description": "用户不存在"}}, "summary": "用户分享获得额外通关机会", "tags": ["weixin"]}}, "/api/v1/weixin/share-config": {"get": {"operationId": "WeixinController_getShareConfig", "parameters": [], "responses": {"200": {"description": "分享配置获取成功", "schema": {"example": {"default": {"title": "一起来挑战词汇游戏！", "path": "/pages/index/index", "imageUrl": "https://example.com/share.jpg", "description": "挑战你的词汇量，看看你能通过多少关！", "type": "default"}, "configs": [{"title": "我在词汇游戏中获得了高分！", "path": "/pages/result/result", "imageUrl": "https://example.com/result-share.jpg", "description": "快来挑战我的记录吧！", "type": "result"}], "total": 2}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinShareConfigListDto"}}}}}, "summary": "获取微信小程序分享配置", "tags": ["weixin"]}}, "/api/v1/weixin/share-config/{type}": {"get": {"operationId": "WeixinController_getShareConfigByType", "parameters": [{"name": "type", "required": true, "in": "path", "description": "分享类型", "schema": {"example": "default", "type": "string"}}], "responses": {"200": {"description": "分享配置获取成功", "schema": {"example": {"title": "一起来挑战词汇游戏！", "path": "/pages/index/index", "imageUrl": "https://example.com/share.jpg", "description": "挑战你的词汇量，看看你能通过多少关！", "type": "default"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeixinShareConfigDto"}}}}, "404": {"description": "指定类型的分享配置不存在"}}, "summary": "根据类型获取微信小程序分享配置", "tags": ["weixin"]}}, "/api/v1/weixin/vip-packages": {"get": {"operationId": "WeixinController_getVipPackages", "parameters": [], "responses": {"200": {"description": "VIP套餐列表获取成功", "schema": {"example": [{"id": "vip_monthly", "name": "VIP月卡", "description": "30天VIP特权，无限制解锁关卡", "price": 2900, "duration": 30, "isActive": true}]}, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VipPackageDto"}}}}}}, "summary": "获取VIP套餐列表", "tags": ["weixin"]}}, "/api/v1/weixin/create-payment": {"post": {"description": "为指定用户和VIP套餐创建微信支付订单，返回小程序支付所需的参数。\n\n订单配置：\n- 订单有效期：30分钟（超时自动失效）\n- 支持重复调用，不会产生重复订单\n\n智能处理逻辑：\n- 如果用户有相同套餐的未完成订单，直接返回该订单的支付参数\n- 如果用户有不同套餐的未完成订单，自动取消旧订单并创建新订单\n- 自动同步微信支付状态，确保订单状态准确性\n- 订单过期后会自动失效，用户可重新创建订单", "operationId": "WeixinController_createPayment", "parameters": [], "responses": {"201": {"description": "支付订单创建成功或返回现有订单支付参数", "schema": {"example": {"appId": "wx1234567890abcdef", "timeStamp": "1640995200", "nonceStr": "abc123def456", "package": "prepay_id=wx123456789012345678901234567890", "signType": "RSA", "paySign": "signature_string_here"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MiniProgramPaymentResponse"}}}}, "400": {"description": "参数错误或创建订单失败"}, "404": {"description": "用户不存在或VIP套餐不存在"}}, "summary": "创建支付订单", "tags": ["weixin"]}}, "/api/v1/weixin/payment-orders": {"get": {"operationId": "WeixinController_getPaymentOrders", "parameters": [{"name": "openid", "required": true, "in": "query", "description": "用户openid", "schema": {"example": "wx_openid_123456", "type": "string"}}], "responses": {"200": {"description": "订单列表获取成功", "content": {"application/json": {"schema": {"example": [{"id": "pay-order-12345678-1234", "out_trade_no": "ORDER_1640995200_1234", "transaction_id": "wx123456789012345678901234567890", "description": "VIP月卡", "total": 2900, "status": "SUCCESS", "vip_package_id": "vip_monthly", "paid_at": "2023-12-01 12:00:00", "created_at": "2023-12-01 11:30:00"}]}}}}}, "summary": "获取用户支付订单列表", "tags": ["weixin"]}}, "/api/v1/weixin/payment-status/{out_trade_no}": {"get": {"operationId": "WeixinController_getPaymentStatus", "parameters": [{"name": "out_trade_no", "required": true, "in": "path", "description": "商户订单号", "schema": {"example": "ORDER_1640995200_1234", "type": "string"}}], "responses": {"200": {"description": "订单查询成功", "content": {"application/json": {"schema": {"example": {"id": "pay-order-12345678-1234", "out_trade_no": "ORDER_1640995200_1234", "transaction_id": "wx123456789012345678901234567890", "description": "VIP月卡", "total": 2900, "status": "SUCCESS", "vip_package_id": "vip_monthly", "paid_at": "2023-12-01 12:00:00", "created_at": "2023-12-01 11:30:00"}}}}}, "404": {"description": "订单不存在"}}, "summary": "查询支付订单状态", "tags": ["weixin"]}}, "/api/v1/settings": {"get": {"operationId": "SettingsController_findAll", "parameters": [], "responses": {"200": {"description": "成功获取设置列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SettingsResponseDto"}}}}}}, "summary": "获取所有设置列表", "tags": ["settings"]}}, "/api/v1/settings/{id}": {"get": {"operationId": "SettingsController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "设置ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取设置详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingsResponseDto"}}}}, "404": {"description": "设置不存在"}}, "summary": "根据ID获取设置详情", "tags": ["settings"]}, "patch": {"operationId": "SettingsController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "设置ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSettingsDto"}}}}, "responses": {"200": {"description": "设置更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingsResponseDto"}}}}, "400": {"description": "请求参数错误"}, "404": {"description": "设置不存在"}}, "summary": "更新设置", "tags": ["settings"]}}, "/api/v1/settings/key/{key}": {"get": {"operationId": "SettingsController_find<PERSON>y<PERSON>ey", "parameters": [{"name": "key", "required": true, "in": "path", "description": "设置键名", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取设置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingsResponseDto"}}}}, "404": {"description": "设置不存在"}}, "summary": "根据键名获取设置", "tags": ["settings"]}, "patch": {"operationId": "SettingsController_updateBy<PERSON>ey", "parameters": [{"name": "key", "required": true, "in": "path", "description": "设置键名", "schema": {"type": "string"}}], "responses": {"200": {"description": "设置更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingsResponseDto"}}}}, "404": {"description": "设置不存在"}}, "summary": "根据键名更新设置值", "tags": ["settings"]}}, "/api/v1/settings/initialize": {"post": {"operationId": "SettingsController_initializeDefaults", "parameters": [], "responses": {"200": {"description": "默认设置初始化成功"}}, "summary": "初始化默认设置", "tags": ["settings"]}}}, "info": {"title": "小游戏 API 文档", "description": "小游戏项目后端接口定义", "version": "1.0", "contact": {}}, "tags": [{"name": "auth", "description": "认证模块"}, {"name": "phrases", "description": "词组管理模块"}, {"name": "thesauruses", "description": "词库管理模块"}, {"name": "levels", "description": "关卡管理模块"}, {"name": "users", "description": "用户管理模块"}, {"name": "weixin", "description": "微信小程序模块"}, {"name": "settings", "description": "设置管理模块"}], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"CreateLevelDto": {"type": "object", "properties": {"thesaurusIds": {"description": "关联的词库ID列表", "example": ["uuid1-thesaurus-xxx", "uuid2-thesaurus-yyy"], "type": "array", "items": {"type": "string"}}, "phraseIds": {"description": "直接选择的词组ID列表", "example": ["uuid1-phrase-xxx", "uuid2-phrase-yyy"], "type": "array", "items": {"type": "string"}}}}, "LevelResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "关卡的唯一ID", "example": "a1b2c3d4-e5f6-7890-1234-567890abcdef"}, "name": {"type": "string", "description": "关卡名称", "example": "第一关：入门"}, "difficulty": {"type": "number", "description": "关卡难度", "example": 1}, "description": {"type": "string", "description": "关卡描述", "example": "这是教学关卡"}, "createdAt": {"type": "string", "description": "创建时间", "example": "2023-10-27 10:30:00"}, "updatedAt": {"type": "string", "description": "最后更新时间", "example": "2023-10-27 10:35:00"}, "thesaurusIds": {"description": "关联的词库ID列表", "example": ["uuid1-thesaurus-xxx"], "type": "array", "items": {"type": "string"}}, "phraseIds": {"description": "关卡包含的词组ID列表", "example": ["uuid1-phrase-abc"], "type": "array", "items": {"type": "string"}}}, "required": ["id", "name", "difficulty", "createdAt", "updatedAt", "thesaurusIds", "phraseIds"]}, "UpdateLevelDto": {"type": "object", "properties": {"thesaurusIds": {"description": "更新关联的词库ID列表", "example": ["uuid3-thesaurus-zzz"], "type": "array", "items": {"type": "string"}}, "phraseIds": {"description": "更新直接选择的词组ID列表", "example": ["uuid1-phrase-xxx", "uuid2-phrase-yyy"], "type": "array", "items": {"type": "string"}}}}, "AddPhraseToLevelDto": {"type": "object", "properties": {"phraseId": {"type": "string", "description": "要添加到关卡的词组ID", "example": "a1b2c3d4-e5f6-7890-1234-567890abcdef"}}, "required": ["phraseId"]}, "CreateThesaurusDto": {"type": "object", "properties": {"name": {"type": "string", "description": "词库名称", "example": "日常用语词库"}, "description": {"type": "string", "description": "词库描述", "example": "包含常见的日常对话用语"}}, "required": ["name"]}, "ThesaurusResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "词库的唯一ID", "example": "t1b2c3d4-e5f6-7890-1234-567890abcdef"}, "name": {"type": "string", "description": "词库名称", "example": "日常用语词库"}, "description": {"type": "string", "description": "词库描述", "example": "包含常见的日常对话用语"}, "phraseIds": {"description": "词库包含的词组ID列表", "example": ["p1a2b3c4-e5f6-7890-1234-567890abcdef", "p2a2b3c4-e5f6-7890-1234-567890abcdef"], "type": "array", "items": {"type": "string"}}, "createdAt": {"type": "string", "description": "创建时间", "example": "2023-10-28 10:30:00"}, "updatedAt": {"type": "string", "description": "最后更新时间", "example": "2023-10-28 10:35:00"}}, "required": ["id", "name", "phraseIds", "createdAt", "updatedAt"]}, "UpdateThesaurusDto": {"type": "object", "properties": {"name": {"type": "string", "description": "词库名称", "example": "日常用语词库"}, "description": {"type": "string", "description": "词库描述", "example": "包含常见的日常对话用语"}}}, "AddPhraseToThesaurusDto": {"type": "object", "properties": {"phraseId": {"type": "string", "description": "要添加的词组ID", "example": "a1b2c3d4-e5f6-7890-1234-567890abcdef"}}, "required": ["phraseId"]}, "CreatePhraseDto": {"type": "object", "properties": {"text": {"type": "string", "description": "词组文本", "example": "Hello World"}, "meaning": {"type": "string", "description": "词组含义", "example": "你好，世界"}, "exampleSentence": {"type": "string", "description": "例句", "example": "When you start programming, the first thing you often do is print \"Hello World\"."}, "tags": {"description": "标签", "example": ["greeting", "common"], "type": "array", "items": {"type": "string"}}}, "required": ["text", "meaning"]}, "PhraseResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "词组的唯一ID", "example": "a1b2c3d4-e5f6-7890-1234-567890abcdef"}, "text": {"type": "string", "description": "词组文本", "example": "Hello World"}, "meaning": {"type": "string", "description": "词组含义", "example": "你好，世界"}, "exampleSentence": {"type": "string", "description": "例句", "example": "When you start programming, the first thing you often do is print \"Hello World\"."}, "tags": {"description": "标签", "example": ["greeting", "common"], "type": "array", "items": {"type": "string"}}, "createdAt": {"type": "string", "description": "创建时间", "example": "2023-10-27 10:30:00"}, "updatedAt": {"type": "string", "description": "最后更新时间", "example": "2023-10-27 10:35:00"}}, "required": ["id", "text", "meaning", "createdAt", "updatedAt"]}, "UpdatePhraseDto": {"type": "object", "properties": {"text": {"type": "string", "description": "词组文本", "example": "Hello World"}, "meaning": {"type": "string", "description": "词组含义", "example": "你好，世界"}, "exampleSentence": {"type": "string", "description": "例句", "example": "When you start programming, the first thing you often do is print \"Hello World\"."}, "tags": {"description": "标签", "example": ["greeting", "common"], "type": "array", "items": {"type": "string"}}}}, "LoginAdminDto": {"type": "object", "properties": {"username": {"type": "string", "description": "管理员用户名", "example": "admin"}, "password": {"type": "string", "description": "管理员密码", "example": "password123"}}, "required": ["username", "password"]}, "CreateUserDto": {"type": "object", "properties": {"phone": {"type": "string", "description": "用户手机号", "example": "13800138000"}, "openid": {"type": "string", "description": "微信用户的openid"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}}}, "UserResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户的唯一ID（8位随机数字）", "example": "12345678"}, "phone": {"type": "string", "description": "用户手机号", "example": "13800138000"}, "openid": {"type": "string", "description": "微信用户的openid"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatarUrl": {"type": "string", "description": "用户头像URL"}, "unlockedLevels": {"type": "number", "description": "用户当前已开启的关卡数", "example": 5}, "completedLevelIds": {"description": "用户已通关的关卡ID列表", "type": "array", "items": {"type": "string"}}, "totalGames": {"type": "number", "description": "用户总游戏次数"}, "totalCompletions": {"type": "number", "description": "用户总通关次数"}, "lastPlayTime": {"type": "string", "description": "最后游戏时间", "example": "2024-01-01 12:00:00"}, "isVip": {"type": "boolean", "description": "VIP状态", "example": false}, "vipExpiresAt": {"type": "string", "description": "VIP过期时间", "example": "2024-01-31 23:59:59"}, "dailyUnlockLimit": {"type": "number", "description": "每日解锁限制次数", "example": 15}, "dailyUnlockCount": {"type": "number", "description": "当日解锁次数", "example": 3}, "dailyShared": {"type": "boolean", "description": "当日是否已分享", "example": false}, "lastPlayDate": {"type": "string", "description": "最后游戏日期（YYYY-MM-DD）", "example": "2025-06-19"}, "totalShares": {"type": "number", "description": "总分享次数", "example": 5}, "createdAt": {"type": "string", "description": "用户注册时间", "example": "2024-01-01 12:00:00"}, "updatedAt": {"type": "string", "description": "用户信息最后更新时间", "example": "2024-01-01 12:00:00"}}, "required": ["id", "unlockedLevels", "completedLevelIds", "totalGames", "totalCompletions", "lastPlayTime", "isVip", "dailyUnlockLimit", "dailyUnlockCount", "dailyShared", "lastPlayDate", "totalShares", "createdAt", "updatedAt"]}, "UpdateUserDto": {"type": "object", "properties": {"unlockedLevels": {"type": "number", "description": "用户当前已开启的关卡数"}, "completedLevelIds": {"description": "用户已通关的关卡ID列表", "type": "array", "items": {"type": "string"}}, "totalGames": {"type": "number", "description": "用户总游戏次数"}, "totalCompletions": {"type": "number", "description": "用户总通关次数"}, "isVip": {"type": "boolean", "description": "VIP状态"}, "vipExpiresAt": {"format": "date-time", "type": "string", "description": "VIP过期时间"}, "dailyUnlockLimit": {"type": "number", "description": "每日解锁限制"}, "dailyUnlockCount": {"type": "number", "description": "当日解锁次数"}, "dailyShared": {"type": "boolean", "description": "当日是否已分享"}, "lastPlayDate": {"type": "string", "description": "最后游戏日期（YYYY-MM-DD）"}, "totalShares": {"type": "number", "description": "总分享次数"}}}, "CompleteLevelDto": {"type": "object", "properties": {"levelId": {"type": "string", "description": "完成的关卡ID"}}, "required": ["levelId"]}, "CreateShareConfigDto": {"type": "object", "properties": {"name": {"type": "string", "description": "分享配置名称", "example": "默认分享配置"}, "title": {"type": "string", "description": "分享标题", "example": "一起来挑战词汇游戏！"}, "path": {"type": "string", "description": "分享路径", "example": "/pages/index/index"}, "imageUrl": {"type": "string", "description": "分享图片URL", "example": "https://example.com/share.jpg"}, "description": {"type": "string", "description": "分享描述", "example": "挑战你的词汇量，看看你能通过多少关！"}, "type": {"type": "string", "description": "分享类型", "example": "default"}, "isActive": {"type": "boolean", "description": "是否启用", "example": true}, "sortOrder": {"type": "number", "description": "排序权重", "example": 1}}, "required": ["name", "title", "path"]}, "ShareConfigResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "分享配置ID", "example": "share-config-001"}, "name": {"type": "string", "description": "分享配置名称", "example": "默认分享配置"}, "title": {"type": "string", "description": "分享标题", "example": "一起来挑战词汇游戏！"}, "path": {"type": "string", "description": "分享路径", "example": "/pages/index/index"}, "imageUrl": {"type": "string", "description": "分享图片URL"}, "description": {"type": "string", "description": "分享描述"}, "type": {"type": "string", "description": "分享类型", "example": "default"}, "isActive": {"type": "boolean", "description": "是否启用", "example": true}, "sortOrder": {"type": "number", "description": "排序权重", "example": 1}, "createdAt": {"type": "string", "description": "创建时间", "example": "2025-06-19T10:00:00.000Z"}, "updatedAt": {"type": "string", "description": "更新时间", "example": "2025-06-19T12:00:00.000Z"}}, "required": ["id", "name", "title", "path", "type", "isActive", "sortOrder", "createdAt", "updatedAt"]}, "UpdateShareConfigDto": {"type": "object", "properties": {"name": {"type": "string", "description": "分享配置名称", "example": "默认分享配置"}, "title": {"type": "string", "description": "分享标题", "example": "一起来挑战词汇游戏！"}, "path": {"type": "string", "description": "分享路径", "example": "/pages/index/index"}, "imageUrl": {"type": "string", "description": "分享图片URL", "example": "https://example.com/share.jpg"}, "description": {"type": "string", "description": "分享描述", "example": "挑战你的词汇量，看看你能通过多少关！"}, "type": {"type": "string", "description": "分享类型", "example": "default"}, "isActive": {"type": "boolean", "description": "是否启用", "example": true}, "sortOrder": {"type": "number", "description": "排序权重", "example": 1}}}, "VipPackageDto": {"type": "object", "properties": {"id": {"type": "string", "description": "套餐ID", "example": "vip_monthly"}, "name": {"type": "string", "description": "套餐名称", "example": "VIP月卡"}, "description": {"type": "string", "description": "套餐描述", "example": "30天VIP特权，无限制解锁关卡"}, "price": {"type": "number", "description": "价格（分）", "example": 2900}, "duration": {"type": "number", "description": "有效期（天）", "example": 30}, "sortOrder": {"type": "number", "description": "排序权重", "example": 1}, "isActive": {"type": "boolean", "description": "是否启用", "example": true}, "createdAt": {"type": "string", "description": "创建时间"}, "updatedAt": {"type": "string", "description": "更新时间"}}, "required": ["id", "name", "description", "price", "duration", "sortOrder", "isActive", "createdAt", "updatedAt"]}, "CreateVipPackageDto": {"type": "object", "properties": {"name": {"type": "string", "description": "套餐名称", "example": "VIP月卡"}, "description": {"type": "string", "description": "套餐描述", "example": "30天VIP特权，无限制解锁关卡"}, "price": {"type": "number", "description": "价格（分）", "example": 2900}, "duration": {"type": "number", "description": "有效期（天）", "example": 30}, "sortOrder": {"type": "number", "description": "排序权重", "example": 1}, "isActive": {"type": "boolean", "description": "是否启用", "example": true}}, "required": ["name", "description", "price", "duration"]}, "UpdateVipPackageDto": {"type": "object", "properties": {"name": {"type": "string", "description": "套餐名称", "example": "VIP月卡"}, "description": {"type": "string", "description": "套餐描述", "example": "30天VIP特权，无限制解锁关卡"}, "price": {"type": "number", "description": "价格（分）", "example": 2900}, "duration": {"type": "number", "description": "有效期（天）", "example": 30}, "sortOrder": {"type": "number", "description": "排序权重", "example": 1}, "isActive": {"type": "boolean", "description": "是否启用", "example": true}}}, "MiniProgramPaymentResponse": {"type": "object", "properties": {"appId": {"type": "string", "description": "小程序ID"}, "timeStamp": {"type": "string", "description": "时间戳"}, "nonceStr": {"type": "string", "description": "随机字符串"}, "package": {"type": "string", "description": "订单详情扩展字符串"}, "signType": {"type": "string", "description": "签名方式"}, "paySign": {"type": "string", "description": "签名"}}, "required": ["appId", "timeStamp", "nonceStr", "package", "signType", "paySign"]}, "PaymentNotifyDto": {"type": "object", "properties": {"id": {"type": "string", "description": "通知ID"}, "create_time": {"type": "string", "description": "通知创建时间"}, "event_type": {"type": "string", "description": "通知类型"}, "resource_type": {"type": "string", "description": "通知数据类型"}, "resource": {"type": "object", "description": "通知资源数据"}, "summary": {"type": "string", "description": "回调摘要"}}, "required": ["id", "create_time", "event_type", "resource_type", "resource", "summary"]}, "WeixinLoginDto": {"type": "object", "properties": {"code": {"type": "string", "description": "微信小程序登录凭证code", "example": "081Kq4Ga1MSox41ufaGa1elzqd4Kq4Gn"}, "phone": {"type": "string", "description": "用户手机号（可选）", "example": "13800138000"}, "nickname": {"type": "string", "description": "用户昵称（可选）", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL（可选）", "example": "https://thirdwx.qlogo.cn/mmopen/xxx"}}, "required": ["code"]}, "WeixinLoginResponseDto": {"type": "object", "properties": {"status": {"type": "string", "description": "登录状态", "example": "success"}, "message": {"type": "string", "description": "响应消息", "example": "登录成功"}, "openid": {"type": "string", "description": "用户openid", "example": "oGZUI0egBJY1zhBYw2KhdUfwVJJE"}, "sessionKey": {"type": "string", "description": "会话密钥（仅用于服务端，不返回给客户端）", "example": "session_key_xxx"}, "userInfo": {"type": "object", "description": "用户信息（登录成功时返回）"}, "unionid": {"type": "string", "description": "unionid（如果小程序绑定了开放平台）"}}, "required": ["status", "message", "openid", "<PERSON><PERSON><PERSON>"]}, "WeixinPhoneBindDto": {"type": "object", "properties": {"openid": {"type": "string", "description": "微信用户openid", "example": "oGZUI0egBJY1zhBYw2KhdUfwVJJE"}, "phone": {"type": "string", "description": "用户手机号", "example": "13800138000"}, "nickname": {"type": "string", "description": "用户昵称（可选）", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL（可选）", "example": "https://thirdwx.qlogo.cn/mmopen/xxx"}}, "required": ["openid", "phone"]}, "WeixinGlobalConfigDto": {"type": "object", "properties": {"appId": {"type": "string", "description": "微信小程序AppID", "example": "wx1234567890abcdef"}, "isConfigured": {"type": "boolean", "description": "微信配置是否完整", "example": true}, "paymentOrderExpireMinutes": {"type": "number", "description": "支付订单过期时间（分钟）", "example": 30}, "environment": {"type": "string", "description": "当前环境", "example": "development", "enum": ["development", "production", "test"]}, "timestamp": {"type": "string", "description": "服务器时间戳", "example": "2024-01-01T12:00:00.000Z"}, "helpUrl": {"type": "string", "description": "帮助页面链接", "example": "https://help.example.com"}, "backgroundMusicUrl": {"type": "string", "description": "背景音乐链接", "example": "https://music.example.com/background.mp3"}, "version": {"type": "string", "description": "应用版本信息", "example": "1.0.0"}, "features": {"type": "object", "description": "功能开关配置", "example": {"enablePayment": true, "enableShare": true, "enableVip": true, "enableMusic": true}}, "gameConfig": {"type": "object", "description": "游戏配置", "example": {"maxLevels": 1000, "dailyUnlockLimit": 15, "shareRewardCount": 5}}}, "required": ["appId", "isConfigured", "paymentOrderExpireMinutes", "environment", "timestamp", "helpUrl", "backgroundMusicUrl"]}, "WeixinAppSettingsDto": {"type": "object", "properties": {"helpUrl": {"type": "string", "description": "帮助页面链接", "example": "https://help.example.com"}, "backgroundMusicUrl": {"type": "string", "description": "背景音乐链接", "example": "https://music.example.com/background.mp3"}}, "required": ["helpUrl", "backgroundMusicUrl"]}, "WeixinUserBindDto": {"type": "object", "properties": {"openid": {"type": "string", "description": "微信用户的openid", "example": "wx_openid_123456"}, "phone": {"type": "string", "description": "用户手机号", "example": "13800138000"}, "nickname": {"type": "string", "description": "用户昵称", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL", "example": "https://wx.qlogo.cn/mmopen/..."}}, "required": ["openid", "phone"]}, "WeixinUserInfoDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户ID", "example": "12345678"}, "maskedPhone": {"type": "string", "description": "加密的手机号（中间4位用*代替）", "example": "138****8000"}, "nickname": {"type": "string", "description": "用户昵称", "example": "微信用户"}, "avatarUrl": {"type": "string", "description": "用户头像URL", "example": "https://wx.qlogo.cn/mmopen/..."}, "unlockedLevels": {"type": "number", "description": "已解锁关卡数", "example": 5}, "completedLevelIds": {"description": "已完成关卡ID列表", "example": ["1", "2", "3"], "type": "array", "items": {"type": "string"}}, "totalGames": {"type": "number", "description": "总游戏次数", "example": 20}, "totalCompletions": {"type": "number", "description": "总通关次数", "example": 15}, "lastPlayTime": {"type": "string", "description": "最后游戏时间", "example": "2025-06-18T12:00:00.000Z"}, "createdAt": {"type": "string", "description": "注册时间", "example": "2025-06-18T10:00:00.000Z"}}, "required": ["id", "unlockedLevels", "completedLevelIds", "totalGames", "totalCompletions", "lastPlayTime", "createdAt"]}, "WeixinLevelDto": {"type": "object", "properties": {"id": {"type": "string", "description": "关卡ID", "example": "level-uuid-123"}, "name": {"type": "string", "description": "关卡名称", "example": "第1关 - 基础词汇"}, "difficulty": {"type": "number", "description": "关卡难度（1-5）", "example": 1}, "description": {"type": "string", "description": "关卡描述", "example": "这是第一关，包含基础词汇"}, "isUnlocked": {"type": "boolean", "description": "是否已解锁", "example": true}, "isCompleted": {"type": "boolean", "description": "是否已完成", "example": false}, "createdAt": {"type": "string", "description": "创建时间", "example": "2025-06-18T10:00:00.000Z"}}, "required": ["id", "name", "difficulty", "isUnlocked", "isCompleted", "createdAt"]}, "WeixinPhraseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "词组ID", "example": "phrase-uuid-1"}, "text": {"type": "string", "description": "词组文本", "example": "Hello World"}, "meaning": {"type": "string", "description": "词组含义", "example": "你好，世界"}, "exampleSentence": {"type": "string", "description": "例句", "example": "When you start programming, the first thing you often do is print \"Hello World\"."}, "tags": {"description": "标签", "example": ["greeting", "common"], "type": "array", "items": {"type": "string"}}}, "required": ["id", "text", "meaning"]}, "WeixinLevelDetailDto": {"type": "object", "properties": {"id": {"type": "string", "description": "关卡ID", "example": "level-uuid-1"}, "name": {"type": "string", "description": "关卡名称", "example": "第1关 - 基础词汇"}, "difficulty": {"type": "number", "description": "关卡难度", "example": 1}, "description": {"type": "string", "description": "关卡描述", "example": "这是第一关，包含基础词汇"}, "isUnlocked": {"type": "boolean", "description": "是否已解锁", "example": true}, "isCompleted": {"type": "boolean", "description": "是否已完成", "example": false}, "phrases": {"description": "关卡包含的词组列表", "type": "array", "items": {"$ref": "#/components/schemas/WeixinPhraseDto"}}, "createdAt": {"type": "string", "description": "关卡创建时间", "example": "2025-06-18T10:00:00.000Z"}}, "required": ["id", "name", "difficulty", "description", "isUnlocked", "isCompleted", "phrases", "createdAt"]}, "WeixinCompleteLevelDto": {"type": "object", "properties": {"openid": {"type": "string", "description": "微信用户openid", "example": "wx_openid_123456"}, "levelId": {"type": "string", "description": "关卡ID", "example": "level-uuid-1"}}, "required": ["openid", "levelId"]}, "WeixinCompleteLevelResponseDto": {"type": "object", "properties": {"message": {"type": "string", "description": "响应消息", "example": "恭喜！关卡通关成功"}, "userId": {"type": "string", "description": "用户ID", "example": "12345678"}, "levelId": {"type": "string", "description": "完成的关卡ID", "example": "level-uuid-1"}, "unlockedLevels": {"type": "number", "description": "新解锁的关卡数", "example": 2}, "totalCompletions": {"type": "number", "description": "总通关次数", "example": 5}, "hasUnlockedNewLevel": {"type": "boolean", "description": "是否解锁了新关卡", "example": true}, "dailyUnlockCount": {"type": "number", "description": "当日解锁次数", "example": 3}, "dailyUnlockLimit": {"type": "number", "description": "每日解锁限制", "example": 15}, "remainingUnlocks": {"type": "number", "description": "剩余解锁次数", "example": 12}, "isVip": {"type": "boolean", "description": "是否为VIP用户", "example": false}}, "required": ["message", "userId", "levelId", "unlockedLevels", "totalCompletions", "hasUnlockedNewLevel"]}, "WeixinDailyStatusDto": {"type": "object", "properties": {"id": {"type": "string", "description": "用户ID", "example": "12345678"}, "dailyUnlockCount": {"type": "number", "description": "当前每日解锁次数", "example": 3}, "dailyUnlockLimit": {"type": "number", "description": "每日解锁限制", "example": 15}, "remainingUnlocks": {"type": "number", "description": "剩余解锁次数", "example": 12}, "dailyShared": {"type": "boolean", "description": "当日是否已分享", "example": false}, "isVip": {"type": "boolean", "description": "是否为VIP用户", "example": false}, "lastPlayDate": {"type": "string", "description": "最后游戏日期", "example": "2025-06-19"}, "totalShares": {"type": "number", "description": "总分享次数", "example": 5}, "canUnlock": {"type": "boolean", "description": "是否可以解锁关卡", "example": true}, "limitReason": {"type": "string", "description": "限制原因（如果不能解锁）"}}, "required": ["id", "dailyUnlockCount", "dailyUnlockLimit", "remainingUnlocks", "dailyShared", "isVip", "lastPlayDate", "totalShares", "canUnlock"]}, "WeixinShareDto": {"type": "object", "properties": {"openid": {"type": "string", "description": "微信用户openid", "example": "oGZUI0egBJY1zhBYw2KhdUfwVJJE"}}, "required": ["openid"]}, "WeixinShareResponseDto": {"type": "object", "properties": {"status": {"type": "string", "description": "分享状态", "example": "success"}, "message": {"type": "string", "description": "响应消息", "example": "分享成功，获得5次额外通关机会！"}, "userId": {"type": "string", "description": "用户ID", "example": "12345678"}, "dailyUnlockCount": {"type": "number", "description": "当前每日解锁次数", "example": 3}, "dailyUnlockLimit": {"type": "number", "description": "每日解锁限制", "example": 20}, "remainingUnlocks": {"type": "number", "description": "剩余解锁次数", "example": 17}, "isVip": {"type": "boolean", "description": "是否为VIP用户", "example": false}, "totalShares": {"type": "number", "description": "总分享次数", "example": 5}}, "required": ["status", "message", "userId", "dailyUnlockCount", "dailyUnlockLimit", "remainingUnlocks", "isVip", "totalShares"]}, "WeixinShareConfigDto": {"type": "object", "properties": {"title": {"type": "string", "description": "分享标题", "example": "一起来挑战词汇游戏！"}, "path": {"type": "string", "description": "分享路径", "example": "/pages/index/index"}, "imageUrl": {"type": "string", "description": "分享图片URL"}, "description": {"type": "string", "description": "分享描述"}, "type": {"type": "string", "description": "分享类型", "example": "default"}}, "required": ["title", "path", "type"]}, "WeixinShareConfigListDto": {"type": "object", "properties": {"default": {"description": "默认分享配置", "allOf": [{"$ref": "#/components/schemas/WeixinShareConfigDto"}]}, "configs": {"description": "其他分享配置列表", "type": "array", "items": {"$ref": "#/components/schemas/WeixinShareConfigDto"}}, "total": {"type": "number", "description": "总配置数量", "example": 3}}, "required": ["default", "configs", "total"]}, "SettingsResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "设置的唯一ID"}, "key": {"type": "string", "description": "设置键名"}, "value": {"type": "string", "description": "设置值"}, "description": {"type": "string", "description": "设置描述"}, "type": {"type": "string", "description": "设置类型", "enum": ["string", "number", "boolean", "url"]}, "createdAt": {"type": "string", "description": "创建时间", "example": "2024-01-01 12:00:00"}, "updatedAt": {"type": "string", "description": "最后更新时间", "example": "2024-01-01 12:00:00"}}, "required": ["id", "key", "value", "type", "createdAt", "updatedAt"]}, "UpdateSettingsDto": {"type": "object", "properties": {"key": {"type": "string", "description": "设置键名", "example": "help_url"}, "value": {"type": "string", "description": "设置值", "example": "https://help.example.com"}, "description": {"type": "string", "description": "设置描述", "example": "帮助页面链接"}, "type": {"type": "string", "description": "设置类型", "enum": ["string", "number", "boolean", "url"], "example": "url"}}}}}}