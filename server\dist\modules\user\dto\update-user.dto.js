"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserDto = void 0;
const mapped_types_1 = require("@nestjs/mapped-types");
const create_user_dto_1 = require("./create-user.dto");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdateUserDto extends (0, mapped_types_1.PartialType)(create_user_dto_1.CreateUserDto) {
    unlockedLevels;
    completedLevelIds;
    totalGames;
    totalCompletions;
    isVip;
    vipExpiresAt;
    dailyUnlockLimit;
    dailyUnlockCount;
    dailyShared;
    lastPlayDate;
    totalShares;
}
exports.UpdateUserDto = UpdateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户当前已开启的关卡数', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateUserDto.prototype, "unlockedLevels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户已通关的关卡ID列表', type: [String], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateUserDto.prototype, "completedLevelIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户总游戏次数', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateUserDto.prototype, "totalGames", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户总通关次数', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateUserDto.prototype, "totalCompletions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP状态', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "isVip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP过期时间', required: false }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateUserDto.prototype, "vipExpiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日解锁限制', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateUserDto.prototype, "dailyUnlockLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日解锁次数', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateUserDto.prototype, "dailyUnlockCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当日是否已分享', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], UpdateUserDto.prototype, "dailyShared", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后游戏日期（YYYY-MM-DD）', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "lastPlayDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总分享次数', required: false }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateUserDto.prototype, "totalShares", void 0);
//# sourceMappingURL=update-user.dto.js.map