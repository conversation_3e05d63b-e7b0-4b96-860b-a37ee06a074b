import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import { PhraseService } from './phrase.service';
import { CreatePhraseDto } from './dto/create-phrase.dto';
import { UpdatePhraseDto } from './dto/update-phrase.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PhraseResponseDto } from './dto/phrase-response.dto';

@ApiTags('phrases') // 将此控制器下的接口归类到 'phrases' 标签
@Controller('phrases') // 路由路径通常用复数形式
export class PhraseController {
  constructor(private readonly phraseService: PhraseService) {}

  @Post()
  @ApiOperation({ summary: '创建新词组' })
  @ApiResponse({ status: 201, description: '词组创建成功', type: PhraseResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  create(@Body() createPhraseDto: CreatePhraseDto) {
    return this.phraseService.create(createPhraseDto);
  }

  @Get()
  @ApiOperation({ summary: '获取所有词组列表' })
  @ApiResponse({ status: 200, description: '成功获取词组列表', type: [PhraseResponseDto] })
  findAll() {
    return this.phraseService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取单个词组' })
  @ApiResponse({ status: 200, description: '成功获取词组', type: PhraseResponseDto })
  @ApiResponse({ status: 404, description: '词组未找到' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.phraseService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新指定ID的词组' })
  @ApiResponse({ status: 200, description: '词组更新成功', type: PhraseResponseDto })
  @ApiResponse({ status: 404, description: '词组未找到' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  update(@Param('id', ParseUUIDPipe) id: string, @Body() updatePhraseDto: UpdatePhraseDto) {
    return this.phraseService.update(id, updatePhraseDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除指定ID的词组' })
  @ApiResponse({ status: 204, description: '词组删除成功' })
  @ApiResponse({ status: 404, description: '词组未找到' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.phraseService.remove(id);
  }
}
