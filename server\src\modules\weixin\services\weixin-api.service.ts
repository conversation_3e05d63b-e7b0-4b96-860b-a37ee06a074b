import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

export interface Code2SessionResponse {
  openid: string;
  session_key: string;
  unionid?: string;
  errcode?: number;
  errmsg?: string;
  rid?: string; // 微信请求ID，用于错误追踪
}

export interface AccessTokenResponse {
  access_token: string;
  expires_in: number;
  errcode?: number;
  errmsg?: string;
}

@Injectable()
export class WeixinApiService {
  private readonly logger = new Logger(WeixinApiService.name);
  private readonly httpClient: AxiosInstance;
  private readonly appId: string;
  private readonly appSecret: string;
  private readonly usedCodes = new Set<string>(); // 用于记录已使用的code
  private readonly codeTimestamps = new Map<string, number>(); // 记录code的使用时间

  constructor(private configService: ConfigService) {
    // 从配置服务获取微信小程序配置
    this.appId = this.configService.get<string>('weixin.appId') || '';
    this.appSecret = this.configService.get<string>('weixin.appSecret') || '';

    if (!this.appId || !this.appSecret) {
      this.logger.warn(
        '⚠️  微信小程序配置缺失，请在环境变量中配置 WEIXIN_APPID 和 WEIXIN_APP_SECRET',
      );
      this.logger.warn(
        `   当前配置: appId=${this.appId ? '已配置' : '未配置'}, appSecret=${this.appSecret ? '已配置' : '未配置'}`,
      );
    } else {
      this.logger.log(
        `✅ 微信小程序配置已加载: appId=${this.appId.substring(0, 8)}...`,
      );
    }

    this.httpClient = axios.create({
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(
          `🔗 微信API请求: ${config.method?.toUpperCase()} ${config.url}`,
        );
        return config;
      },
      (error) => {
        this.logger.error('❌ 微信API请求失败:', error);
        return Promise.reject(error);
      },
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(
          `✅ 微信API响应: ${response.status} ${response.config.url}`,
        );
        return response;
      },
      (error) => {
        this.logger.error(
          '❌ 微信API响应错误:',
          error.response?.data || error.message,
        );
        return Promise.reject(error);
      },
    );
  }

  /**
   * 调用微信 code2Session 接口，获取用户 openid 和 session_key
   * @param code 微信小程序登录凭证
   * @param retryCount 重试次数，默认为0
   * @returns 用户信息
   */
  async code2Session(
    code: string,
    retryCount: number = 0,
  ): Promise<Code2SessionResponse> {
    const maxRetries = 2; // 最大重试次数
    const retryDelay = 1000; // 重试延迟（毫秒）

    try {
      // 验证输入参数
      if (!code || typeof code !== 'string' || code.trim().length === 0) {
        this.logger.error('❌ 微信登录失败: code参数无效');
        throw new BadRequestException('微信登录失败: 登录凭证无效');
      }

      // 检查code是否已被使用（防止重复调用）
      const trimmedCode = code.trim();
      if (this.usedCodes.has(trimmedCode)) {
        this.logger.error(`❌ 微信登录失败: code已被使用 - ${trimmedCode.substring(0, 8)}...`);
        throw new BadRequestException('微信登录失败: 登录凭证已被使用，请重新获取');
      }

      // 检查配置
      if (!this.isConfigured()) {
        this.logger.error('❌ 微信登录失败: 微信小程序配置不完整');
        throw new BadRequestException('微信登录失败: 服务配置错误');
      }

      const url = 'https://api.weixin.qq.com/sns/jscode2session';
      const params = {
        appid: this.appId,
        secret: this.appSecret,
        js_code: trimmedCode,
        grant_type: 'authorization_code',
      };

      // 标记code为正在使用（防止并发请求）
      this.usedCodes.add(trimmedCode);
      this.codeTimestamps.set(trimmedCode, Date.now());

      // 清理过期的code（超过10分钟的）
      this.cleanupExpiredCodes();

      this.logger.log(
        `🔐 调用微信登录接口: code=${code.substring(0, 8)}... (尝试 ${retryCount + 1}/${maxRetries + 1})`,
      );
      this.logger.debug(
        `📋 请求参数: appid=${this.appId.substring(0, 8)}..., grant_type=${params.grant_type}`,
      );

      const response = await this.httpClient.get<Code2SessionResponse>(url, {
        params,
        timeout: 8000, // 8秒超时
      });
      const data = response.data;

      this.logger.debug(`📥 微信API响应: ${JSON.stringify(data)}`);

      // 检查微信接口返回的错误
      if (data.errcode) {
        const errorMsg = this.getErrorMessage(data.errcode);
        this.logger.error(
          `❌ 微信登录失败: ${data.errcode} - ${data.errmsg || 'Unknown error'}`,
        );
        this.logger.error(
          `🔍 错误详情: code=${code.substring(0, 8)}..., rid=${data.rid || 'N/A'}`,
        );

        // 对于特定错误码，不进行重试
        const noRetryErrors = [40029, 40013, 40125, 40163, 40226]; // 无效code、无效AppID、无效AppSecret、code已被使用、用户被拦截
        if (noRetryErrors.includes(data.errcode)) {
          throw new BadRequestException(
            `微信登录失败: ${errorMsg} (错误码: ${data.errcode})`,
          );
        }

        // 对于可重试的错误，如果还有重试次数则重试
        if (retryCount < maxRetries) {
          this.logger.warn(
            `⏳ 微信登录重试中... (${retryCount + 1}/${maxRetries})`,
          );
          await this.delay(retryDelay * (retryCount + 1)); // 递增延迟
          return this.code2Session(code, retryCount + 1);
        }

        throw new BadRequestException(
          `微信登录失败: ${errorMsg} (错误码: ${data.errcode})`,
        );
      }

      if (!data.openid || !data.session_key) {
        this.logger.error('❌ 微信登录响应数据不完整:', data);
        throw new BadRequestException('微信登录失败: 响应数据不完整');
      }

      this.logger.log(
        `✅ 微信登录成功: openid=${data.openid.substring(0, 8)}..., session_key=${data.session_key.substring(0, 8)}...`,
      );
      return data;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      // 网络错误或其他异常，可以重试
      if (retryCount < maxRetries) {
        this.logger.warn(
          `⏳ 网络异常，微信登录重试中... (${retryCount + 1}/${maxRetries}): ${error.message}`,
        );
        await this.delay(retryDelay * (retryCount + 1));
        return this.code2Session(code, retryCount + 1);
      }

      this.logger.error('❌ 调用微信登录接口异常:', error);
      throw new BadRequestException('微信登录服务暂时不可用，请稍后重试');
    }
  }

  /**
   * 延迟函数
   * @param ms 延迟毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 清理过期的code（超过10分钟的）
   */
  private cleanupExpiredCodes(): void {
    const now = Date.now();
    const expireTime = 10 * 60 * 1000; // 10分钟

    for (const [code, timestamp] of this.codeTimestamps.entries()) {
      if (now - timestamp > expireTime) {
        this.usedCodes.delete(code);
        this.codeTimestamps.delete(code);
        this.logger.debug(`🧹 清理过期code: ${code.substring(0, 8)}...`);
      }
    }
  }

  /**
   * 获取微信接口调用凭证 access_token
   * @returns access_token 信息
   */
  async getAccessToken(): Promise<AccessTokenResponse> {
    try {
      const url = 'https://api.weixin.qq.com/cgi-bin/token';
      const params = {
        grant_type: 'client_credential',
        appid: this.appId,
        secret: this.appSecret,
      };

      this.logger.log('🔑 获取微信 access_token...');

      const response = await this.httpClient.get<AccessTokenResponse>(url, {
        params,
      });
      const data = response.data;

      if (data.errcode) {
        this.logger.error(
          `❌ 获取 access_token 失败: ${data.errcode} - ${data.errmsg}`,
        );
        throw new BadRequestException(
          `获取访问令牌失败: ${this.getErrorMessage(data.errcode)}`,
        );
      }

      this.logger.log('✅ 获取 access_token 成功');
      return data;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error('❌ 获取 access_token 异常:', error);
      throw new BadRequestException('获取访问令牌失败，请稍后重试');
    }
  }

  /**
   * 检验登录态是否有效
   * @param openid 用户openid
   * @param sessionKey 会话密钥
   * @returns 是否有效
   */
  async checkSessionKey(openid: string, sessionKey: string): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();
      const url = 'https://api.weixin.qq.com/wxa/checksession';
      const params = {
        access_token: accessToken.access_token,
        signature: this.generateSignature(sessionKey),
        openid: openid,
        sig_method: 'hmac_sha256',
      };

      const response = await this.httpClient.get(url, { params });
      const data = response.data;

      if (data.errcode === 0) {
        this.logger.log(`✅ 登录态有效: openid=${openid.substring(0, 8)}...`);
        return true;
      } else {
        this.logger.log(
          `❌ 登录态无效: openid=${openid.substring(0, 8)}... errcode=${data.errcode}`,
        );
        return false;
      }
    } catch (error) {
      this.logger.error('❌ 检验登录态异常:', error);
      return false;
    }
  }

  /**
   * 生成签名（简化版本，实际项目中需要更复杂的签名算法）
   * @param sessionKey 会话密钥
   * @returns 签名
   */
  private generateSignature(sessionKey: string): string {
    // 这里是简化的签名生成，实际项目中需要根据微信文档实现正确的签名算法
    return Buffer.from(sessionKey).toString('base64');
  }

  /**
   * 获取微信错误码对应的中文描述
   * @param errcode 错误码
   * @returns 错误描述
   */
  private getErrorMessage(errcode: number): string {
    const errorMessages: Record<number, string> = {
      // 登录相关错误
      40029: '登录凭证无效或已过期，请重新登录',
      40013: 'AppID无效，请检查小程序配置',
      40125: 'AppSecret无效，请检查小程序配置',
      40163: '登录凭证已被使用，请重新获取',

      // 频率限制
      45011: 'API调用太频繁，请稍候再试',

      // 用户状态
      40226: '用户被拦截，请联系客服',

      // 系统错误
      [-1]: '系统繁忙，请稍后重试',
      40001: '获取access_token时AppSecret错误，或者access_token无效',
      40002: '不合法的凭证类型',
      40003: '不合法的OpenID',
      40004: '不合法的媒体文件类型',
      40005: '不合法的文件类型',
      40006: '不合法的文件大小',
      40007: '不合法的媒体文件id',
      40008: '不合法的消息类型',
      40009: '不合法的图片文件大小',
      40010: '不合法的语音文件大小',
      40011: '不合法的视频文件大小',
      40012: '不合法的缩略图文件大小',

      // 网络相关
      50001: '用户未授权该api',
      50002: '用户受限，可能是违规后接口被封禁',

      // 其他常见错误
      41001: '缺少access_token参数',
      41002: '缺少appid参数',
      41003: '缺少refresh_token参数',
      41004: '缺少secret参数',
      41005: '缺少多媒体文件数据',
      41006: '缺少media_id参数',
      41007: '缺少子菜单数据',
      41008: '缺少oauth code',
      41009: '缺少openid',
    };

    const message = errorMessages[errcode];
    if (message) {
      return message;
    }

    // 根据错误码范围提供通用描述
    if (errcode >= 40000 && errcode < 41000) {
      return `请求参数错误 (${errcode})`;
    } else if (errcode >= 41000 && errcode < 42000) {
      return `缺少必要参数 (${errcode})`;
    } else if (errcode >= 42000 && errcode < 43000) {
      return `参数值无效 (${errcode})`;
    } else if (errcode >= 43000 && errcode < 44000) {
      return `请求过于频繁 (${errcode})`;
    } else if (errcode >= 44000 && errcode < 45000) {
      return `多媒体文件为空 (${errcode})`;
    } else if (errcode >= 45000 && errcode < 46000) {
      return `多媒体文件大小超过限制 (${errcode})`;
    } else if (errcode >= 46000 && errcode < 47000) {
      return `控制流异常 (${errcode})`;
    } else if (errcode >= 47000 && errcode < 48000) {
      return `JSON/XML内容异常 (${errcode})`;
    } else if (errcode >= 48000 && errcode < 49000) {
      return `url异常 (${errcode})`;
    } else if (errcode >= 50000 && errcode < 51000) {
      return `用户未授权 (${errcode})`;
    }

    return `未知错误 (${errcode})`;
  }

  /**
   * 检查微信小程序配置是否完整
   * @returns 配置是否完整
   */
  isConfigured(): boolean {
    return !!(this.appId && this.appSecret);
  }

  /**
   * 获取配置信息（用于调试）
   * @returns 配置信息
   */
  getConfig() {
    return {
      appId: this.appId ? `${this.appId.substring(0, 8)}...` : '未配置',
      appSecret: this.appSecret ? '已配置' : '未配置',
      isConfigured: this.isConfigured(),
    };
  }
}
