import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type ThesaurusDocument = Thesaurus & Document;

@Schema({ timestamps: true })
export class Thesaurus {
  @ApiProperty({ description: '词库的唯一ID' })
  @Prop({ required: true, unique: true })
  id: string;

  @ApiProperty({ description: '词库名称' })
  @Prop({ required: true })
  name: string;

  @ApiProperty({ description: '词库描述', required: false })
  @Prop()
  description?: string;

  @ApiProperty({ description: '存储关联的词组ID', type: [String] })
  @Prop({ type: [String], default: [] })
  phraseIds: string[];

  @ApiProperty({ description: '创建时间 (Date Object)' })
  createdAt: Date;

  @ApiProperty({ description: '最后更新时间 (Date Object)' })
  updatedAt: Date;
}

export const ThesaurusSchema = SchemaFactory.createForClass(Thesaurus);

// 为了向后兼容，保留ThesaurusEntity类型别名
export type ThesaurusEntity = Thesaurus;