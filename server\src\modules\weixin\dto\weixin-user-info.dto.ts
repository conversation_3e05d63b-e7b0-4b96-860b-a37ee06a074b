import { ApiProperty } from '@nestjs/swagger';

export class WeixinUserInfoDto {
  @ApiProperty({ description: '用户ID', example: '12345678' })
  id: string;

  @ApiProperty({ description: '加密的手机号（中间4位用*代替）', example: '138****8000', required: false })
  maskedPhone?: string;

  @ApiProperty({ description: '用户昵称', required: false, example: '微信用户' })
  nickname?: string;

  @ApiProperty({ description: '用户头像URL', required: false, example: 'https://wx.qlogo.cn/mmopen/...' })
  avatarUrl?: string;

  @ApiProperty({ description: '已解锁关卡数', example: 5 })
  unlockedLevels: number;

  @ApiProperty({ description: '已完成关卡ID列表', type: [String], example: ['1', '2', '3'] })
  completedLevelIds: string[];

  @ApiProperty({ description: '总游戏次数', example: 20 })
  totalGames: number;

  @ApiProperty({ description: '总通关次数', example: 15 })
  totalCompletions: number;

  @ApiProperty({ description: '最后游戏时间', example: '2025-06-18T12:00:00.000Z' })
  lastPlayTime: string;

  @ApiProperty({ description: '注册时间', example: '2025-06-18T10:00:00.000Z' })
  createdAt: string;
}

export class WeixinLevelDto {
  @ApiProperty({ description: '关卡ID', example: 'level-uuid-123' })
  id: string;

  @ApiProperty({ description: '关卡名称', example: '第1关 - 基础词汇' })
  name: string;

  @ApiProperty({ description: '关卡难度（1-5）', example: 1 })
  difficulty: number;

  @ApiProperty({ description: '关卡描述', required: false, example: '这是第一关，包含基础词汇' })
  description?: string;

  @ApiProperty({ description: '是否已解锁', example: true })
  isUnlocked: boolean;

  @ApiProperty({ description: '是否已完成', example: false })
  isCompleted: boolean;

  @ApiProperty({ description: '创建时间', example: '2025-06-18T10:00:00.000Z' })
  createdAt: string;
}
