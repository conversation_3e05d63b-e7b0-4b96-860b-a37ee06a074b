/**
 * 数据验证脚本：验证每日解锁限制字段的迁移状态
 * 
 * 验证内容：
 * - 检查新字段是否存在
 * - 检查旧字段是否已清理
 * - 验证数据完整性
 * 
 * 运行方式：
 * node scripts/verify-daily-fields.js
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || '**************************************************';
const DATABASE_NAME = process.env.DATABASE_NAME || 'vocabulary-game';

async function verifyDailyFields() {
  let client;
  
  try {
    console.log('🔍 开始验证数据库字段...');
    console.log(`📍 连接数据库: ${MONGODB_URI}`);
    
    // 连接数据库
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const usersCollection = db.collection('users');
    
    // 检查集合是否存在
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      console.log('⚠️  用户集合不存在');
      return;
    }
    
    // 获取所有用户
    const users = await usersCollection.find({}).toArray();
    console.log(`📊 找到 ${users.length} 个用户`);
    
    if (users.length === 0) {
      console.log('✅ 没有用户数据');
      return;
    }
    
    // 统计数据
    const stats = {
      total: users.length,
      hasNewFields: 0,
      hasOldFields: 0,
      hasAllRequiredFields: 0,
      missingFields: [],
      fieldDistribution: {
        dailyUnlockLimit: {},
        dailyUnlockCount: {},
        isVip: { true: 0, false: 0 },
        dailyShared: { true: 0, false: 0 }
      }
    };
    
    // 分析每个用户
    for (const user of users) {
      let hasAllRequired = true;
      
      // 检查新字段
      const hasNewFields = user.hasOwnProperty('dailyUnlockLimit') && user.hasOwnProperty('dailyUnlockCount');
      if (hasNewFields) {
        stats.hasNewFields++;
      }
      
      // 检查旧字段
      const hasOldFields = user.hasOwnProperty('dailyPlayLimit') || user.hasOwnProperty('dailyPlayCount');
      if (hasOldFields) {
        stats.hasOldFields++;
      }
      
      // 检查必需字段
      const requiredFields = ['dailyUnlockLimit', 'dailyUnlockCount', 'isVip', 'dailyShared', 'totalShares', 'lastPlayDate'];
      for (const field of requiredFields) {
        if (!user.hasOwnProperty(field)) {
          hasAllRequired = false;
          if (!stats.missingFields.includes(field)) {
            stats.missingFields.push(field);
          }
        }
      }
      
      if (hasAllRequired) {
        stats.hasAllRequiredFields++;
      }
      
      // 统计字段值分布
      if (user.hasOwnProperty('dailyUnlockLimit')) {
        const limit = user.dailyUnlockLimit;
        stats.fieldDistribution.dailyUnlockLimit[limit] = (stats.fieldDistribution.dailyUnlockLimit[limit] || 0) + 1;
      }
      
      if (user.hasOwnProperty('dailyUnlockCount')) {
        const count = user.dailyUnlockCount;
        stats.fieldDistribution.dailyUnlockCount[count] = (stats.fieldDistribution.dailyUnlockCount[count] || 0) + 1;
      }
      
      if (user.hasOwnProperty('isVip')) {
        stats.fieldDistribution.isVip[user.isVip]++;
      }
      
      if (user.hasOwnProperty('dailyShared')) {
        stats.fieldDistribution.dailyShared[user.dailyShared]++;
      }
    }
    
    // 输出验证结果
    console.log('\n📈 验证结果:');
    console.log('='.repeat(50));
    
    console.log(`\n🔢 基本统计:`);
    console.log(`   - 总用户数: ${stats.total}`);
    console.log(`   - 拥有新字段的用户: ${stats.hasNewFields} (${(stats.hasNewFields/stats.total*100).toFixed(1)}%)`);
    console.log(`   - 仍有旧字段的用户: ${stats.hasOldFields} (${(stats.hasOldFields/stats.total*100).toFixed(1)}%)`);
    console.log(`   - 拥有所有必需字段的用户: ${stats.hasAllRequiredFields} (${(stats.hasAllRequiredFields/stats.total*100).toFixed(1)}%)`);
    
    if (stats.missingFields.length > 0) {
      console.log(`\n⚠️  缺失的字段:`);
      for (const field of stats.missingFields) {
        const missingCount = users.filter(user => !user.hasOwnProperty(field)).length;
        console.log(`   - ${field}: ${missingCount} 个用户缺失`);
      }
    }
    
    console.log(`\n📊 字段值分布:`);
    
    console.log(`   dailyUnlockLimit:`);
    Object.entries(stats.fieldDistribution.dailyUnlockLimit)
      .sort(([a], [b]) => Number(a) - Number(b))
      .forEach(([value, count]) => {
        console.log(`     - ${value}: ${count} 个用户`);
      });
    
    console.log(`   dailyUnlockCount:`);
    Object.entries(stats.fieldDistribution.dailyUnlockCount)
      .sort(([a], [b]) => Number(a) - Number(b))
      .forEach(([value, count]) => {
        console.log(`     - ${value}: ${count} 个用户`);
      });
    
    console.log(`   isVip:`);
    Object.entries(stats.fieldDistribution.isVip).forEach(([value, count]) => {
      console.log(`     - ${value}: ${count} 个用户`);
    });
    
    console.log(`   dailyShared:`);
    Object.entries(stats.fieldDistribution.dailyShared).forEach(([value, count]) => {
      console.log(`     - ${value}: ${count} 个用户`);
    });
    
    // 显示示例用户数据
    console.log(`\n👤 示例用户数据 (前5个):`);
    const sampleUsers = users.slice(0, 5);
    for (const user of sampleUsers) {
      console.log(`   用户 ${user.id || user._id}:`);
      console.log(`     - dailyUnlockLimit: ${user.dailyUnlockLimit || 'undefined'}`);
      console.log(`     - dailyUnlockCount: ${user.dailyUnlockCount || 'undefined'}`);
      console.log(`     - isVip: ${user.isVip || 'undefined'}`);
      console.log(`     - dailyShared: ${user.dailyShared || 'undefined'}`);
      console.log(`     - totalShares: ${user.totalShares || 'undefined'}`);
      console.log(`     - lastPlayDate: ${user.lastPlayDate || 'undefined'}`);
      
      // 检查旧字段
      if (user.hasOwnProperty('dailyPlayLimit') || user.hasOwnProperty('dailyPlayCount')) {
        console.log(`     ⚠️  仍有旧字段: dailyPlayLimit=${user.dailyPlayLimit}, dailyPlayCount=${user.dailyPlayCount}`);
      }
      console.log('');
    }
    
    // 总结
    console.log('='.repeat(50));
    if (stats.hasNewFields === stats.total && stats.hasOldFields === 0 && stats.hasAllRequiredFields === stats.total) {
      console.log('✅ 迁移验证通过！所有用户都有正确的字段结构');
    } else {
      console.log('⚠️  迁移验证发现问题：');
      if (stats.hasNewFields < stats.total) {
        console.log(`   - ${stats.total - stats.hasNewFields} 个用户缺少新字段`);
      }
      if (stats.hasOldFields > 0) {
        console.log(`   - ${stats.hasOldFields} 个用户仍有旧字段`);
      }
      if (stats.hasAllRequiredFields < stats.total) {
        console.log(`   - ${stats.total - stats.hasAllRequiredFields} 个用户缺少必需字段`);
      }
    }
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  try {
    await verifyDailyFields();
    process.exit(0);
  } catch (error) {
    console.error('💥 验证失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { verifyDailyFields };
