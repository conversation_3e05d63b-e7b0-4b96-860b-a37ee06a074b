import { Model } from 'mongoose';
import { PaymentOrderDocument, VipPackageDocument } from './entities/payment-order.entity';
import { WechatPayService } from './services/wechat-pay.service';
import { UserService } from '../user/user.service';
import { MiniProgramPaymentResponse, VipPackageDto, CreateVipPackageDto, UpdateVipPackageDto } from './dto/payment.dto';
export declare class PaymentService {
    private paymentOrderModel;
    private vipPackageModel;
    private wechatPayService;
    private userService;
    private readonly logger;
    private readonly PAYMENT_ORDER_EXPIRE_MINUTES;
    private readonly PAYMENT_ORDER_EXPIRE_MS;
    constructor(paymentOrderModel: Model<PaymentOrderDocument>, vipPackageModel: Model<VipPackageDocument>, wechatPayService: WechatPayService, userService: UserService);
    private generateOrderNo;
    getPaymentOrderExpireMinutes(): number;
    private checkAndHandleExistingOrder;
    private syncWechatOrderStatus;
    private generateUniquePaymentOrderId;
    getVipPackages(): Promise<VipPackageDto[]>;
    getAllVipPackages(): Promise<VipPackageDto[]>;
    createVipPackage(createDto: CreateVipPackageDto): Promise<VipPackageDto>;
    updateVipPackage(id: string, updateDto: UpdateVipPackageDto): Promise<VipPackageDto>;
    deleteVipPackage(id: string): Promise<void>;
    getVipPackageById(id: string): Promise<VipPackageDto>;
    private getVipPackageDocument;
    createPaymentOrder(openid: string, packageId: string): Promise<MiniProgramPaymentResponse>;
    handlePaymentNotify(notifyData: any): Promise<void>;
    private activateUserVip;
    queryPaymentOrder(out_trade_no: string): Promise<any>;
    getPaymentOrdersList(params: {
        page: number;
        pageSize: number;
        search?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        userId?: string;
    }): Promise<any>;
    getUserPaymentOrders(userId: string): Promise<any[]>;
    getPaymentOrderStats(params: {
        startDate?: string;
        endDate?: string;
    }): Promise<any>;
    getPaymentOrderById(id: string): Promise<any>;
    refreshPaymentOrderStatus(out_trade_no: string): Promise<any>;
    cancelPaymentOrder(out_trade_no: string, userId: string): Promise<void>;
}
