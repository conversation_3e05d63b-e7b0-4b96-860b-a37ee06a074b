import { PaymentService } from './payment.service';
import { WechatPayService } from './services/wechat-pay.service';
import { MiniProgramPaymentResponse, VipPackageDto, PaymentNotifyDto, CreateVipPackageDto, UpdateVipPackageDto } from './dto/payment.dto';
export declare class PaymentController {
    private readonly paymentService;
    private readonly wechatPayService;
    private readonly logger;
    constructor(paymentService: PaymentService, wechatPayService: WechatPayService);
    getWechatPayConfig(): Promise<any>;
    getVipPackages(): Promise<VipPackageDto[]>;
    getVipPackageById(id: string): Promise<VipPackageDto>;
    createVipPackage(createDto: CreateVipPackageDto): Promise<VipPackageDto>;
    updateVipPackage(id: string, updateDto: UpdateVipPackageDto): Promise<VipPackageDto>;
    deleteVipPackage(id: string): Promise<{
        message: string;
    }>;
    createPaymentOrder(body: {
        openid: string;
        packageId: string;
    }): Promise<MiniProgramPaymentResponse>;
    handlePaymentNotify(headers: any, body: PaymentNotifyDto): Promise<{
        code: string;
        message: string;
    }>;
    queryPaymentOrder(out_trade_no: string): Promise<any>;
    refreshPaymentOrderStatus(out_trade_no: string): Promise<any>;
    getPaymentOrders(page?: string, pageSize?: string, search?: string, status?: string, startDate?: string, endDate?: string, userId?: string): Promise<any>;
    getUserPaymentOrders(userId: string): Promise<any[]>;
    getPaymentOrderStats(startDate?: string, endDate?: string): Promise<any>;
    getPaymentOrderById(id: string): Promise<any>;
    cancelPaymentOrder(out_trade_no: string, body: {
        userId: string;
    }): Promise<{
        message: string;
    }>;
}
