import { ApiProperty } from '@nestjs/swagger';

export class LevelResponseDto {
  @ApiProperty({ description: '关卡的唯一ID', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
  id: string;

  @ApiProperty({ description: '关卡名称', example: '第一关：入门' })
  name: string;

  @ApiProperty({ description: '关卡难度', example: 1 })
  difficulty: number;

  @ApiProperty({ description: '关卡描述', example: '这是教学关卡', required: false })
  description?: string;

  @ApiProperty({ description: '创建时间', example: '2023-10-27 10:30:00' })
  createdAt: string;

  @ApiProperty({ description: '最后更新时间', example: '2023-10-27 10:35:00' })
  updatedAt: string;

  @ApiProperty({ description: '关联的词库ID列表', type: [String], example: ['uuid1-thesaurus-xxx'] })
  thesaurusIds: string[];

  @ApiProperty({ description: '关卡包含的词组ID列表', type: [String], example: ['uuid1-phrase-abc'] })
  phraseIds: string[];
}