import { ApiProperty } from '@nestjs/swagger';

export class WeixinShareConfigDto {
  @ApiProperty({ description: '分享标题', example: '一起来挑战词汇游戏！' })
  title: string;

  @ApiProperty({ description: '分享路径', example: '/pages/index/index' })
  path: string;

  @ApiProperty({ description: '分享图片URL', required: false })
  imageUrl?: string;

  @ApiProperty({ description: '分享描述', required: false })
  description?: string;

  @ApiProperty({ description: '分享类型', example: 'default' })
  type: string;
}

export class WeixinShareConfigListDto {
  @ApiProperty({ description: '默认分享配置' })
  default: WeixinShareConfigDto;

  @ApiProperty({ description: '其他分享配置列表', type: [WeixinShareConfigDto] })
  configs: WeixinShareConfigDto[];

  @ApiProperty({ description: '总配置数量', example: 3 })
  total: number;
}
