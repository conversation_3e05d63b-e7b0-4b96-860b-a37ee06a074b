import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ThesaurusService } from './thesaurus.service';
import { ThesaurusController } from './thesaurus.controller';
import { PhraseModule } from '../phrase/phrase.module'; // 导入 PhraseModule
import { Thesaurus, ThesaurusSchema } from './entities/thesaurus.entity';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Thesaurus.name, schema: ThesaurusSchema }]),
    forwardRef(() => PhraseModule), // 使用 forwardRef 处理循环依赖
  ],
  controllers: [ThesaurusController],
  providers: [ThesaurusService],
  exports: [ThesaurusService], // 导出 ThesaurusService 以便其他模块使用
})
export class ThesaurusModule {}