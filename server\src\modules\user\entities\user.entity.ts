import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
  @ApiProperty({ description: '用户的唯一ID（8位随机数字）', example: '12345678' })
  @Prop({ required: true, unique: true, length: 8 })
  id: string;

  @ApiProperty({ description: '用户手机号', example: '13800138000' })
  @Prop({
    required: false,
    unique: true,
    sparse: true,
    set: (value: string) => value === '' ? undefined : value
  })
  phone?: string;

  @ApiProperty({ description: '微信用户的openid', required: false })
  @Prop({ unique: true, sparse: true })
  openid?: string;

  @ApiProperty({ description: '用户昵称', required: false })
  @Prop()
  nickname?: string;

  @ApiProperty({ description: '用户头像URL', required: false })
  @Prop()
  avatarUrl?: string;

  @ApiProperty({ description: '用户当前已开启的关卡数', example: 5 })
  @Prop({ default: 1 })
  unlockedLevels: number;

  @ApiProperty({ description: '用户已通关的关卡ID列表', type: [String] })
  @Prop({ type: [String], default: [] })
  completedLevelIds: string[];

  @ApiProperty({ description: '用户总游戏次数' })
  @Prop({ default: 0 })
  totalGames: number;

  @ApiProperty({ description: '用户总通关次数' })
  @Prop({ default: 0 })
  totalCompletions: number;

  @ApiProperty({ description: '最后游戏时间' })
  @Prop({ default: Date.now })
  lastPlayTime: Date;

  @ApiProperty({ description: 'VIP状态', example: false })
  @Prop({ default: false })
  isVip: boolean;

  @ApiProperty({ description: 'VIP过期时间', required: false })
  @Prop()
  vipExpiresAt?: Date;

  @ApiProperty({ description: '每日解锁限制次数', example: 15 })
  @Prop({ default: 15 })
  dailyUnlockLimit: number;

  @ApiProperty({ description: '当日解锁次数', example: 3 })
  @Prop({ default: 0 })
  dailyUnlockCount: number;

  @ApiProperty({ description: '当日是否已分享', example: false })
  @Prop({ default: false })
  dailyShared: boolean;

  @ApiProperty({ description: '最后游戏日期（YYYY-MM-DD）', example: '2025-06-19' })
  @Prop({ default: () => new Date().toISOString().split('T')[0] })
  lastPlayDate: string;

  @ApiProperty({ description: '总分享次数', example: 5 })
  @Prop({ default: 0 })
  totalShares: number;

  @ApiProperty({ description: '用户注册时间' })
  createdAt: Date;

  @ApiProperty({ description: '用户信息最后更新时间' })
  updatedAt: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);

// 为了向后兼容，保留UserEntity类型别名
export interface UserEntity {
  id: string;
  phone?: string;
  openid?: string;
  nickname?: string;
  avatarUrl?: string;
  unlockedLevels: number;
  completedLevelIds: string[];
  totalGames: number;
  totalCompletions: number;
  lastPlayTime: Date;
  isVip: boolean;
  vipExpiresAt?: Date;
  dailyUnlockLimit: number;
  dailyUnlockCount: number;
  dailyShared: boolean;
  lastPlayDate: string;
  totalShares: number;
  createdAt: Date;
  updatedAt: Date;
}
