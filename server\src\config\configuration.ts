import * as Jo<PERSON> from 'joi';

export interface DatabaseConfig {
  uri: string;
  name: string;
}

export interface ServerConfig {
  port: number;
  environment: string;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
}

export interface WeixinConfig {
  appId: string;
  appSecret: string;
  mchId?: string;
  apiKey?: string;
  serialNo?: string;
  notifyUrl?: string;
  privateKeyPath?: string;
}

export interface AppConfig {
  database: DatabaseConfig;
  server: ServerConfig;
  jwt: JwtConfig;
  weixin: WeixinConfig;
  debug: boolean;
}

// 环境变量验证 Schema
export const configValidationSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'staging', 'production')
    .default('development'),
  PORT: Joi.number().default(3001),
  MONGODB_URI: Joi.string().required(),
  MONGODB_DB_NAME: Joi.string().default('xxl_dev_db'),
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('24h'),
  DEBUG: Joi.boolean().default(false),
  // 微信小程序配置
  WEIXIN_APPID: Joi.string().optional(),
  WEIXIN_APP_SECRET: Joi.string().optional(),
  WEIXIN_MCH_ID: Joi.string().optional(),
  WEIXIN_API_KEY: Joi.string().optional(),
  WEIXIN_SERIAL_NO: Joi.string().optional(),
  WEIXIN_NOTIFY_URL: Joi.string().optional(),
  WEIXIN_PRIVATE_KEY_PATH: Joi.string().optional(),
});

export default (): AppConfig => ({
  database: {
    uri: process.env.MONGODB_URI!,
    name: process.env.MONGODB_DB_NAME || 'xxl_dev_db',
  },
  server: {
    port: parseInt(process.env.PORT || '3001', 10),
    environment: process.env.NODE_ENV || 'development',
  },
  jwt: {
    secret: process.env.JWT_SECRET!,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  weixin: {
    appId: process.env.WEIXIN_APPID || '',
    appSecret: process.env.WEIXIN_APP_SECRET || '',
    mchId: process.env.WEIXIN_MCH_ID || '',
    apiKey: process.env.WEIXIN_API_KEY || '',
    serialNo: process.env.WEIXIN_SERIAL_NO || '',
    notifyUrl: process.env.WEIXIN_NOTIFY_URL || '',
    privateKeyPath: process.env.WEIXIN_PRIVATE_KEY_PATH || '',
  },
  debug: process.env.DEBUG === 'true',
});
