import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class WeixinCompleteLevelDto {
  @ApiProperty({ description: '微信用户openid', example: 'wx_openid_123456' })
  @IsString()
  @IsNotEmpty({ message: 'openid不能为空' })
  openid: string;

  @ApiProperty({ description: '关卡ID', example: 'level-uuid-1' })
  @IsString()
  @IsNotEmpty({ message: '关卡ID不能为空' })
  levelId: string;
}

export class WeixinCompleteLevelResponseDto {
  @ApiProperty({ description: '响应消息', example: '恭喜！关卡通关成功' })
  message: string;

  @ApiProperty({ description: '用户ID', example: '12345678' })
  userId: string;

  @ApiProperty({ description: '完成的关卡ID', example: 'level-uuid-1' })
  levelId: string;

  @ApiProperty({ description: '新解锁的关卡数', example: 2 })
  unlockedLevels: number;

  @ApiProperty({ description: '总通关次数', example: 5 })
  totalCompletions: number;

  @ApiProperty({ description: '是否解锁了新关卡', example: true })
  hasUnlockedNewLevel: boolean;

  @ApiProperty({ description: '当日解锁次数', example: 3, required: false })
  dailyUnlockCount?: number;

  @ApiProperty({ description: '每日解锁限制', example: 15, required: false })
  dailyUnlockLimit?: number;

  @ApiProperty({ description: '剩余解锁次数', example: 12, required: false })
  remainingUnlocks?: number;

  @ApiProperty({ description: '是否为VIP用户', example: false, required: false })
  isVip?: boolean;
}
