import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateThesaurusDto } from './dto/create-thesaurus.dto';
import { UpdateThesaurusDto } from './dto/update-thesaurus.dto';
import { Thesaurus, ThesaurusDocument, ThesaurusEntity } from './entities/thesaurus.entity';
import { v4 as uuidv4 } from 'uuid';
import { ThesaurusResponseDto } from './dto/thesaurus-response.dto';
import { formatDate } from '../../common/utils/date-formatter';
import { PhraseService } from '../phrase/phrase.service'; // 用于验证 phraseId

@Injectable()
export class ThesaurusService {
  constructor(
    @InjectModel(Thesaurus.name) private thesaurusModel: Model<ThesaurusDocument>,
    @Inject(forwardRef(() => PhraseService)) // 处理循环依赖
    private readonly phraseService: PhraseService,
  ) {}

  private _mapToResponseDto(thesaurus: ThesaurusDocument): ThesaurusResponseDto {
    return {
      id: thesaurus.id,
      name: thesaurus.name,
      description: thesaurus.description,
      phraseIds: thesaurus.phraseIds,
      createdAt: formatDate(thesaurus.createdAt),
      updatedAt: formatDate(thesaurus.updatedAt),
    };
  }

  async getThesaurusEntity(id: string): Promise<ThesaurusDocument> { // 改为 public 供 LevelService 使用
    const thesaurus = await this.thesaurusModel.findOne({ id }).exec();
    if (!thesaurus) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的词库`);
    }
    return thesaurus;
  }

  async create(createThesaurusDto: CreateThesaurusDto): Promise<ThesaurusResponseDto> {
    const newThesaurus = new this.thesaurusModel({
      id: uuidv4(),
      name: createThesaurusDto.name,
      description: createThesaurusDto.description,
      phraseIds: [], // 初始化为空数组
    });
    const savedThesaurus = await newThesaurus.save();
    return this._mapToResponseDto(savedThesaurus);
  }

  async findAll(): Promise<ThesaurusResponseDto[]> {
    const thesauruses = await this.thesaurusModel.find().exec();
    return thesauruses.map(thesaurus => this._mapToResponseDto(thesaurus));
  }

  async findOne(id: string): Promise<ThesaurusResponseDto> {
    const thesaurus = await this.getThesaurusEntity(id);
    return this._mapToResponseDto(thesaurus);
  }

  async update(id: string, updateThesaurusDto: UpdateThesaurusDto): Promise<ThesaurusResponseDto> {
    const updatedThesaurus = await this.thesaurusModel.findOneAndUpdate(
      { id },
      { ...updateThesaurusDto, updatedAt: new Date() },
      { new: true }
    ).exec();

    if (!updatedThesaurus) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的词库`);
    }

    return this._mapToResponseDto(updatedThesaurus);
  }

  async remove(id: string): Promise<void> {
    const result = await this.thesaurusModel.deleteOne({ id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的词库`);
    }
  }

  async addPhrase(thesaurusId: string, phraseId: string): Promise<ThesaurusResponseDto> {
    const thesaurus = await this.getThesaurusEntity(thesaurusId);
    // 验证 phraseId 是否有效 (确保 PhraseService 有 findOne 或类似方法)
    await this.phraseService.findOne(phraseId); // 如果 phrase 不存在，这里会抛出 NotFoundException

    if (thesaurus.phraseIds.includes(phraseId)) {
      throw new BadRequestException(`词组 ID "${phraseId}" 已存在于词库 "${thesaurusId}" 中`);
    }

    thesaurus.phraseIds.push(phraseId);
    const updatedThesaurus = await thesaurus.save();
    return this._mapToResponseDto(updatedThesaurus);
  }

  async removePhrase(thesaurusId: string, phraseId: string): Promise<ThesaurusResponseDto> {
    const thesaurus = await this.getThesaurusEntity(thesaurusId);
    const phraseIndexInThesaurus = thesaurus.phraseIds.indexOf(phraseId);

    if (phraseIndexInThesaurus === -1) {
      throw new NotFoundException(`词组 ID "${phraseId}" 不在词库 "${thesaurusId}" 中`);
    }

    thesaurus.phraseIds.splice(phraseIndexInThesaurus, 1);
    const updatedThesaurus = await thesaurus.save();
    return this._mapToResponseDto(updatedThesaurus);
  }

  async isPhraseInAnyOfThesauruses(phraseId: string, thesaurusIds: string[]): Promise<boolean> {
    for (const thesaurusId of thesaurusIds) {
      try {
        const thesaurus = await this.getThesaurusEntity(thesaurusId);
        if (thesaurus.phraseIds.includes(phraseId)) {
          return true;
        }
      } catch (error) {
        // 如果某个词库ID无效，可以选择忽略或记录日志，这里假设忽略
        console.warn(`检查词组是否存在于词库时，词库ID "${thesaurusId}" 未找到。`);
      }
    }
    return false;
  }
}