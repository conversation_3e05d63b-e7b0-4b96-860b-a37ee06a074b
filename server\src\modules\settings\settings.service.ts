import { Injectable, NotFoundException, BadRequestException, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { Settings, SettingsDocument } from './entities/settings.entity';
import { CreateSettingsDto } from './dto/create-settings.dto';
import { UpdateSettingsDto } from './dto/update-settings.dto';
import { SettingsResponseDto } from './dto/settings-response.dto';
import { formatDate } from '../../common/utils/date-formatter';

@Injectable()
export class SettingsService implements OnModuleInit {
  constructor(
    @InjectModel(Settings.name) private settingsModel: Model<SettingsDocument>,
  ) {}

  async create(createSettingsDto: CreateSettingsDto): Promise<SettingsResponseDto> {
    // 检查键名是否已存在
    const existingSettings = await this.settingsModel.findOne({ key: createSettingsDto.key }).exec();
    if (existingSettings) {
      throw new BadRequestException(`设置键名 "${createSettingsDto.key}" 已存在`);
    }

    const newSettings = new this.settingsModel({
      id: uuidv4(),
      key: createSettingsDto.key,
      value: createSettingsDto.value,
      description: createSettingsDto.description,
      type: createSettingsDto.type,
    });

    const savedSettings = await newSettings.save();
    return this._mapToSettingsResponseDto(savedSettings);
  }

  async findAll(): Promise<SettingsResponseDto[]> {
    const settings = await this.settingsModel.find().exec();
    return settings.map(setting => this._mapToSettingsResponseDto(setting));
  }

  async findOne(id: string): Promise<SettingsResponseDto> {
    const settings = await this.getSettingsEntity(id);
    return this._mapToSettingsResponseDto(settings);
  }

  async findByKey(key: string): Promise<SettingsResponseDto> {
    const settings = await this.settingsModel.findOne({ key }).exec();
    if (!settings) {
      throw new NotFoundException(`未找到键名为 "${key}" 的设置`);
    }
    return this._mapToSettingsResponseDto(settings);
  }

  async update(id: string, updateSettingsDto: UpdateSettingsDto): Promise<SettingsResponseDto> {
    const settings = await this.getSettingsEntity(id);

    // 如果要更新键名，检查新键名是否已存在
    if (updateSettingsDto.key && updateSettingsDto.key !== settings.key) {
      const existingSettings = await this.settingsModel.findOne({ key: updateSettingsDto.key }).exec();
      if (existingSettings) {
        throw new BadRequestException(`设置键名 "${updateSettingsDto.key}" 已存在`);
      }
    }

    Object.assign(settings, updateSettingsDto);
    const updatedSettings = await settings.save();
    return this._mapToSettingsResponseDto(updatedSettings);
  }

  async updateByKey(key: string, value: string): Promise<SettingsResponseDto> {
    const settings = await this.settingsModel.findOne({ key }).exec();
    if (!settings) {
      throw new NotFoundException(`未找到键名为 "${key}" 的设置`);
    }

    settings.value = value;
    const updatedSettings = await settings.save();
    return this._mapToSettingsResponseDto(updatedSettings);
  }

  async remove(id: string): Promise<void> {
    const settings = await this.getSettingsEntity(id);
    await this.settingsModel.deleteOne({ id }).exec();
  }

  async getSettingsEntity(id: string): Promise<SettingsDocument> {
    const settings = await this.settingsModel.findOne({ id }).exec();
    if (!settings) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的设置`);
    }
    return settings;
  }

  // 初始化默认设置
  async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = [
      {
        key: 'help_url',
        value: 'https://help.example.com',
        description: '帮助页面链接',
        type: 'url',
      },
      {
        key: 'background_music_url',
        value: 'https://music.example.com/background.mp3',
        description: '背景音乐链接',
        type: 'url',
      },
    ];

    for (const setting of defaultSettings) {
      const existing = await this.settingsModel.findOne({ key: setting.key }).exec();
      if (!existing) {
        const newSetting = new this.settingsModel({
          id: uuidv4(),
          ...setting,
        });
        await newSetting.save();
        console.log(`✅ 初始化设置: ${setting.key} = ${setting.value}`);
      }
    }
  }

  // 应用启动时自动初始化
  async onModuleInit(): Promise<void> {
    try {
      await this.initializeDefaultSettings();
      console.log('✅ 设置模块初始化完成');
    } catch (error) {
      console.error('❌ 设置模块初始化失败:', error);
    }
  }

  private _mapToSettingsResponseDto(settings: SettingsDocument): SettingsResponseDto {
    return {
      id: settings.id,
      key: settings.key,
      value: settings.value,
      description: settings.description,
      type: settings.type,
      createdAt: formatDate(settings.createdAt),
      updatedAt: formatDate(settings.updatedAt),
    };
  }
}
