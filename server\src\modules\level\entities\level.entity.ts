import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type LevelDocument = Level & Document;

@Schema({ timestamps: true })
export class Level {
  @ApiProperty({ description: '关卡的唯一ID' })
  @Prop({ required: true, unique: true })
  id: string;

  @ApiProperty({ description: '关卡名称' })
  @Prop({ required: true })
  name: string;

  @ApiProperty({ description: '关卡难度' })
  @Prop({ required: true, min: 1, max: 5 })
  difficulty: number;

  @ApiProperty({ description: '关卡描述', required: false })
  @Prop()
  description?: string;

  @ApiProperty({ description: '关联的词库ID列表', type: [String] })
  @Prop({ type: [String], default: [] })
  thesaurusIds: string[];

  @ApiProperty({ description: '关卡包含的词组ID列表', type: [String] })
  @Prop({ type: [String], default: [] })
  phraseIds: string[];

  @ApiProperty({ description: '创建时间 (Date Object)' })
  createdAt: Date;

  @ApiProperty({ description: '最后更新时间 (Date Object)' })
  updatedAt: Date;
}

export const LevelSchema = SchemaFactory.createForClass(Level);

// 为了向后兼容，保留LevelEntity类型别名
export type LevelEntity = Level;