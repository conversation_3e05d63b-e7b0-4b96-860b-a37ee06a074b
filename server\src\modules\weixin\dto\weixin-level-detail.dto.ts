import { ApiProperty } from '@nestjs/swagger';

export class WeixinPhraseDto {
  @ApiProperty({ description: '词组ID', example: 'phrase-uuid-1' })
  id: string;

  @ApiProperty({ description: '词组文本', example: 'Hello World' })
  text: string;

  @ApiProperty({ description: '词组含义', example: '你好，世界' })
  meaning: string;

  @ApiProperty({ description: '例句', example: 'When you start programming, the first thing you often do is print "Hello World".', required: false })
  exampleSentence?: string;

  @ApiProperty({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] })
  tags?: string[];
}

export class WeixinLevelDetailDto {
  @ApiProperty({ description: '关卡ID', example: 'level-uuid-1' })
  id: string;

  @ApiProperty({ description: '关卡名称', example: '第1关 - 基础词汇' })
  name: string;

  @ApiProperty({ description: '关卡难度', example: 1 })
  difficulty: number;

  @ApiProperty({ description: '关卡描述', example: '这是第一关，包含基础词汇' })
  description?: string;

  @ApiProperty({ description: '是否已解锁', example: true })
  isUnlocked: boolean;

  @ApiProperty({ description: '是否已完成', example: false })
  isCompleted: boolean;

  @ApiProperty({ description: '关卡包含的词组列表', type: [WeixinPhraseDto] })
  phrases: WeixinPhraseDto[];

  @ApiProperty({ description: '关卡创建时间', example: '2025-06-18T10:00:00.000Z' })
  createdAt: string;
}
