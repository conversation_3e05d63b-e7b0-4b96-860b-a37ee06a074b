import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type PhraseDocument = Phrase & Document;

@Schema({ timestamps: true })
export class Phrase {
  @ApiProperty({ description: '词组的唯一ID', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
  @Prop({ required: true, unique: true })
  id: string;

  @ApiProperty({ description: '词组文本', example: 'Hello World' })
  @Prop({ required: true })
  text: string;

  @ApiProperty({ description: '词组含义', example: '你好，世界' })
  @Prop({ required: true })
  meaning: string;

  @ApiProperty({ description: '例句', example: 'When you start programming, the first thing you often do is print "Hello World".', required: false })
  @Prop()
  exampleSentence?: string;

  @ApiProperty({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] })
  @Prop({ type: [String], default: [] })
  tags?: string[];

  @ApiProperty({ description: '创建时间 (Date Object)' })
  createdAt: Date;

  @ApiProperty({ description: '最后更新时间 (Date Object)' })
  updatedAt: Date;
}

export const PhraseSchema = SchemaFactory.createForClass(Phrase);

// 为了向后兼容，保留PhraseEntity类型别名
export type PhraseEntity = Phrase;