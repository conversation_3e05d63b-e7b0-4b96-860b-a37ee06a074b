import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type SettingsDocument = Settings & Document;

@Schema({ timestamps: true })
export class Settings {
  @ApiProperty({ description: '设置的唯一ID' })
  @Prop({ required: true, unique: true })
  id: string;

  @ApiProperty({ description: '设置键名' })
  @Prop({ required: true, unique: true })
  key: string;

  @ApiProperty({ description: '设置值' })
  @Prop({ required: true })
  value: string;

  @ApiProperty({ description: '设置描述', required: false })
  @Prop()
  description?: string;

  @ApiProperty({ description: '设置类型', enum: ['string', 'number', 'boolean', 'url'] })
  @Prop({ required: true, enum: ['string', 'number', 'boolean', 'url'], default: 'string' })
  type: string;

  @ApiProperty({ description: '创建时间 (Date Object)' })
  createdAt: Date;

  @ApiProperty({ description: '最后更新时间 (Date Object)' })
  updatedAt: Date;
}

export const SettingsSchema = SchemaFactory.createForClass(Settings);

// 为了向后兼容，保留SettingsEntity类型别名
export type SettingsEntity = Settings;
