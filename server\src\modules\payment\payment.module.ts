import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { WechatPayService } from './services/wechat-pay.service';
import { PaymentOrder, PaymentOrderSchema, VipPackage, VipPackageSchema } from './entities/payment-order.entity';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PaymentOrder.name, schema: PaymentOrderSchema },
      { name: VipPackage.name, schema: VipPackageSchema },
    ]),
    UserModule,
  ],
  controllers: [PaymentController],
  providers: [PaymentService, WechatPayService],
  exports: [PaymentService, WechatPayService],
})
export class PaymentModule {}
