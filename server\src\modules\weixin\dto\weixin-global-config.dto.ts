import { ApiProperty } from '@nestjs/swagger';

export class WeixinGlobalConfigDto {
  @ApiProperty({ 
    description: '微信小程序AppID', 
    example: 'wx1234567890abcdef',
    required: true
  })
  appId: string;

  @ApiProperty({ 
    description: '微信配置是否完整', 
    example: true,
    required: true
  })
  isConfigured: boolean;

  @ApiProperty({ 
    description: '支付订单过期时间（分钟）', 
    example: 30,
    required: true
  })
  paymentOrderExpireMinutes: number;

  @ApiProperty({ 
    description: '当前环境', 
    example: 'development',
    enum: ['development', 'production', 'test'],
    required: true
  })
  environment: string;

  @ApiProperty({ 
    description: '服务器时间戳', 
    example: '2024-01-01T12:00:00.000Z',
    required: true
  })
  timestamp: string;

  @ApiProperty({ 
    description: '帮助页面链接', 
    example: 'https://help.example.com',
    required: true
  })
  helpUrl: string;

  @ApiProperty({ 
    description: '背景音乐链接', 
    example: 'https://music.example.com/background.mp3',
    required: true
  })
  backgroundMusicUrl: string;

  @ApiProperty({ 
    description: '应用版本信息', 
    example: '1.0.0',
    required: false
  })
  version?: string;

  @ApiProperty({ 
    description: '功能开关配置', 
    example: {
      enablePayment: true,
      enableShare: true,
      enableVip: true,
      enableMusic: true
    },
    required: false
  })
  features?: {
    enablePayment: boolean;
    enableShare: boolean;
    enableVip: boolean;
    enableMusic: boolean;
  };

  @ApiProperty({ 
    description: '游戏配置', 
    example: {
      maxLevels: 1000,
      dailyUnlockLimit: 15,
      shareRewardCount: 5
    },
    required: false
  })
  gameConfig?: {
    maxLevels: number;
    dailyUnlockLimit: number;
    shareRewardCount: number;
  };
}
