import { UserService } from '../user/user.service';
import { LevelService } from '../level/level.service';
import { WeixinUserBindDto } from './dto/weixin-user-bind.dto';
import { WeixinUserInfoDto, WeixinLevelDto } from './dto/weixin-user-info.dto';
import { WeixinLevelDetailDto } from './dto/weixin-level-detail.dto';
import { WeixinCompleteLevelDto, WeixinCompleteLevelResponseDto } from './dto/weixin-complete-level.dto';
import { WeixinLoginDto, WeixinLoginResponseDto, WeixinPhoneBindDto } from './dto/weixin-login.dto';
import { WeixinShareDto, WeixinShareResponseDto, WeixinDailyStatusDto } from './dto/weixin-daily-play.dto';
import { WeixinShareConfigDto, WeixinShareConfigListDto } from './dto/weixin-share-config.dto';
import { WeixinAppSettingsDto } from './dto/weixin-settings.dto';
import { WeixinGlobalConfigDto } from './dto/weixin-global-config.dto';
import { WeixinApiService } from './services/weixin-api.service';
import { ShareService } from '../share/share.service';
import { PaymentService } from '../payment/payment.service';
import { SettingsService } from '../settings/settings.service';
import { MiniProgramPaymentResponse, VipPackageDto } from '../payment/dto/payment.dto';
export declare class WeixinController {
    private readonly userService;
    private readonly levelService;
    private readonly weixinApiService;
    private readonly shareService;
    private readonly paymentService;
    private readonly settingsService;
    constructor(userService: UserService, levelService: LevelService, weixinApiService: WeixinApiService, shareService: ShareService, paymentService: PaymentService, settingsService: SettingsService);
    login(loginDto: WeixinLoginDto): Promise<WeixinLoginResponseDto>;
    bindPhone(bindDto: WeixinPhoneBindDto): Promise<WeixinLoginResponseDto>;
    getConfig(): Promise<{
        paymentOrderExpireMinutes: number;
        timestamp: string;
        environment: string;
        appId: string;
        appSecret: string;
        isConfigured: boolean;
    }>;
    getGlobalConfig(): Promise<WeixinGlobalConfigDto>;
    getAppSettings(): Promise<WeixinAppSettingsDto>;
    bindUser(bindDto: WeixinUserBindDto): Promise<WeixinUserInfoDto>;
    getUserInfo(openid: string): Promise<WeixinUserInfoDto>;
    getLevelsWithProgress(openid: string): Promise<WeixinLevelDto[]>;
    getUserInfoById(id: string): Promise<WeixinUserInfoDto>;
    getLevelDetail(levelId: string, openid: string): Promise<WeixinLevelDetailDto>;
    completeLevel(completeLevelDto: WeixinCompleteLevelDto): Promise<WeixinCompleteLevelResponseDto>;
    getDailyStatus(openid: string): Promise<WeixinDailyStatusDto>;
    share(shareDto: WeixinShareDto): Promise<WeixinShareResponseDto>;
    getShareConfig(): Promise<WeixinShareConfigListDto>;
    getShareConfigByType(type: string): Promise<WeixinShareConfigDto | null>;
    getVipPackages(): Promise<VipPackageDto[]>;
    createPayment(body: {
        openid: string;
        packageId: string;
    }): Promise<MiniProgramPaymentResponse>;
    getPaymentOrders(openid: string): Promise<any[]>;
    getPaymentStatus(out_trade_no: string): Promise<any>;
}
