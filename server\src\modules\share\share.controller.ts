import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { ShareService } from './share.service';
import { CreateShareConfigDto } from './dto/create-share-config.dto';
import { UpdateShareConfigDto } from './dto/update-share-config.dto';
import { ShareConfigResponseDto } from './dto/share-config-response.dto';

@ApiTags('分享管理')
@Controller('share')
export class ShareController {
  constructor(private readonly shareService: ShareService) {}

  @Post()
  @ApiOperation({ summary: '创建分享配置' })
  @ApiResponse({
    status: 201,
    description: '分享配置创建成功',
    type: ShareConfigResponseDto,
    schema: {
      example: {
        id: 'share-config-001',
        name: '默认分享配置',
        title: '一起来挑战词汇游戏！',
        path: '/pages/index/index',
        imageUrl: 'https://example.com/share.jpg',
        description: '挑战你的词汇量，看看你能通过多少关！',
        type: 'default',
        isActive: true,
        sortOrder: 1,
        createdAt: '2025-06-19T10:00:00.000Z',
        updatedAt: '2025-06-19T10:00:00.000Z'
      }
    }
  })
  @ApiResponse({ status: 400, description: '参数无效或默认配置已存在' })
  async create(@Body() createShareConfigDto: CreateShareConfigDto): Promise<ShareConfigResponseDto> {
    return this.shareService.create(createShareConfigDto);
  }

  @Get()
  @ApiOperation({ summary: '获取所有分享配置' })
  @ApiResponse({
    status: 200,
    description: '分享配置列表获取成功',
    type: [ShareConfigResponseDto]
  })
  async findAll(): Promise<ShareConfigResponseDto[]> {
    return this.shareService.findAll();
  }

  @Get('active')
  @ApiOperation({ summary: '获取启用的分享配置' })
  @ApiResponse({
    status: 200,
    description: '启用的分享配置列表获取成功',
    type: [ShareConfigResponseDto]
  })
  async findActive(): Promise<ShareConfigResponseDto[]> {
    return this.shareService.findActive();
  }

  @Get('default')
  @ApiOperation({ summary: '获取默认分享配置' })
  @ApiResponse({
    status: 200,
    description: '默认分享配置获取成功',
    type: ShareConfigResponseDto
  })
  @ApiResponse({ status: 404, description: '默认分享配置不存在' })
  async getDefault(): Promise<ShareConfigResponseDto | null> {
    return this.shareService.getDefaultConfig();
  }

  @Get('type/:type')
  @ApiOperation({ summary: '根据类型获取分享配置' })
  @ApiParam({ name: 'type', description: '分享类型', example: 'default' })
  @ApiResponse({
    status: 200,
    description: '分享配置获取成功',
    type: ShareConfigResponseDto
  })
  @ApiResponse({ status: 404, description: '指定类型的分享配置不存在' })
  async findByType(@Param('type') type: string): Promise<ShareConfigResponseDto | null> {
    return this.shareService.findByType(type);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取分享配置' })
  @ApiParam({ name: 'id', description: '分享配置ID', example: 'share-config-001' })
  @ApiResponse({
    status: 200,
    description: '分享配置获取成功',
    type: ShareConfigResponseDto
  })
  @ApiResponse({ status: 404, description: '分享配置不存在' })
  async findOne(@Param('id') id: string): Promise<ShareConfigResponseDto> {
    return this.shareService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新分享配置' })
  @ApiParam({ name: 'id', description: '分享配置ID', example: 'share-config-001' })
  @ApiResponse({
    status: 200,
    description: '分享配置更新成功',
    type: ShareConfigResponseDto
  })
  @ApiResponse({ status: 404, description: '分享配置不存在' })
  @ApiResponse({ status: 400, description: '参数无效' })
  async update(
    @Param('id') id: string,
    @Body() updateShareConfigDto: UpdateShareConfigDto
  ): Promise<ShareConfigResponseDto> {
    return this.shareService.update(id, updateShareConfigDto);
  }

  @Put(':id/toggle')
  @ApiOperation({ summary: '启用/禁用分享配置' })
  @ApiParam({ name: 'id', description: '分享配置ID', example: 'share-config-001' })
  @ApiResponse({
    status: 200,
    description: '分享配置状态切换成功',
    type: ShareConfigResponseDto
  })
  @ApiResponse({ status: 404, description: '分享配置不存在' })
  async toggleActive(@Param('id') id: string): Promise<ShareConfigResponseDto> {
    return this.shareService.toggleActive(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除分享配置' })
  @ApiParam({ name: 'id', description: '分享配置ID', example: 'share-config-001' })
  @ApiResponse({ status: 200, description: '分享配置删除成功' })
  @ApiResponse({ status: 404, description: '分享配置不存在' })
  @ApiResponse({ status: 400, description: '不能删除默认配置' })
  async remove(@Param('id') id: string): Promise<{ message: string }> {
    await this.shareService.remove(id);
    return { message: '分享配置删除成功' };
  }
}
