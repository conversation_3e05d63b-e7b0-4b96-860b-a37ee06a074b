import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateLevelDto } from './dto/create-level.dto';
import { UpdateLevelDto } from './dto/update-level.dto';
import { Level, LevelDocument, LevelEntity } from './entities/level.entity';
import { v4 as uuidv4 } from 'uuid';
import { LevelResponseDto } from './dto/level-response.dto';
import { formatDate } from '../../common/utils/date-formatter';
import { ThesaurusService } from '../thesaurus/thesaurus.service';
import { PhraseService } from '../phrase/phrase.service';

@Injectable()
export class LevelService {
  private readonly MAX_LEVELS = 1000;

  constructor(
    @InjectModel(Level.name) private levelModel: Model<LevelDocument>,
    @Inject(forwardRef(() => ThesaurusService)) private readonly thesaurusService: ThesaurusService,
    @Inject(forwardRef(() => PhraseService)) private readonly phraseService: PhraseService,
  ) {}
  async getLevelEntity(id: string): Promise<LevelDocument> { // 改为 public
    const level = await this.levelModel.findOne({ id }).exec();
    if (!level) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的关卡`);
    }
    return level;
  }

  private _mapToLevelResponseDto(level: LevelDocument): LevelResponseDto {
    return {
      id: level.id,
      name: level.name,
      difficulty: level.difficulty,
      description: level.description,
      thesaurusIds: level.thesaurusIds,
      phraseIds: level.phraseIds,
      createdAt: formatDate(level.createdAt),
      updatedAt: formatDate(level.updatedAt),
    };
  }

  async create(createLevelDto: CreateLevelDto): Promise<LevelResponseDto> {
    const currentCount = await this.levelModel.countDocuments().exec();
    if (currentCount >= this.MAX_LEVELS) {
      throw new BadRequestException(`关卡数量已达到上限 ${this.MAX_LEVELS} 个`);
    }

    // 验证至少提供了词库ID或词组ID中的一种
    if ((!createLevelDto.thesaurusIds || createLevelDto.thesaurusIds.length === 0) &&
        (!createLevelDto.phraseIds || createLevelDto.phraseIds.length === 0)) {
      throw new BadRequestException('必须至少提供词库ID或词组ID中的一种');
    }

    const thesaurusIds = createLevelDto.thesaurusIds || [];
    const phraseIds = createLevelDto.phraseIds || [];

    // 验证 thesaurusIds 是否都有效
    for (const thesaurusId of thesaurusIds) {
      await this.thesaurusService.getThesaurusEntity(thesaurusId);
    }

    // 验证 phraseIds 是否都有效
    for (const phraseId of phraseIds) {
      await this.phraseService.findOne(phraseId);
    }

    const newLevel = new this.levelModel({
      id: uuidv4(),
      name: createLevelDto.name,
      difficulty: createLevelDto.difficulty,
      description: createLevelDto.description,
      thesaurusIds: thesaurusIds,
      phraseIds: phraseIds,
    });

    const savedLevel = await newLevel.save();
    return this._mapToLevelResponseDto(savedLevel);
  }

  async findAll(): Promise<LevelResponseDto[]> {
    const levels = await this.levelModel.find().exec();
    return levels.map(level => this._mapToLevelResponseDto(level));
  }

  async findOne(id: string): Promise<LevelResponseDto> {
    const level = await this.getLevelEntity(id);
    return this._mapToLevelResponseDto(level);
  }

  async update(id: string, updateLevelDto: UpdateLevelDto): Promise<LevelResponseDto> {
    const existingLevel = await this.getLevelEntity(id);

    // 准备更新数据
    const updateData: any = {};

    // 基础字段更新
    if ((updateLevelDto as any).name !== undefined) {
      updateData.name = (updateLevelDto as any).name;
    }
    if ((updateLevelDto as any).difficulty !== undefined) {
      updateData.difficulty = (updateLevelDto as any).difficulty;
    }
    if ((updateLevelDto as any).description !== undefined) {
      updateData.description = (updateLevelDto as any).description;
    }

    // 处理 thesaurusIds 更新
    if (updateLevelDto.thesaurusIds !== undefined) {
      // 如果 thesaurusIds 不为空且有内容，则验证
      if (updateLevelDto.thesaurusIds && updateLevelDto.thesaurusIds.length > 0) {
        for (const thesaurusId of updateLevelDto.thesaurusIds) {
          await this.thesaurusService.getThesaurusEntity(thesaurusId);
        }
      }
      updateData.thesaurusIds = updateLevelDto.thesaurusIds || [];
    }

    // 处理 phraseIds 更新
    if (updateLevelDto.phraseIds !== undefined) {
      // 如果 phraseIds 不为空且有内容，则验证
      if (updateLevelDto.phraseIds && updateLevelDto.phraseIds.length > 0) {
        for (const phraseId of updateLevelDto.phraseIds) {
          await this.phraseService.findOne(phraseId);
        }
      }
      updateData.phraseIds = updateLevelDto.phraseIds || [];
    }

    // 验证更新后的关卡至少包含词库或词组
    const finalThesaurusIds = updateData.thesaurusIds !== undefined ? updateData.thesaurusIds : existingLevel.thesaurusIds;
    const finalPhraseIds = updateData.phraseIds !== undefined ? updateData.phraseIds : existingLevel.phraseIds;

    if ((!finalThesaurusIds || finalThesaurusIds.length === 0) &&
        (!finalPhraseIds || finalPhraseIds.length === 0)) {
      throw new BadRequestException('关卡必须至少包含词库或词组中的一种');
    }

    const updatedLevel = await this.levelModel.findOneAndUpdate(
      { id },
      { ...updateData, updatedAt: new Date() },
      { new: true }
    ).exec();

    if (!updatedLevel) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的关卡`);
    }

    return this._mapToLevelResponseDto(updatedLevel);
  }

  async remove(id: string): Promise<void> {
    const result = await this.levelModel.deleteOne({ id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的关卡`);
    }
  }

  async addPhrase(levelId: string, phraseId: string): Promise<LevelResponseDto> {
    const level = await this.getLevelEntity(levelId);

    // 1. 验证词组存在
    await this.phraseService.findOne(phraseId); // findOne 返回 DTO，能找到即表示存在

    // 2. 验证词组是否属于关卡已关联的词库
    const isPhraseInAssociatedThesaurus = await this.thesaurusService.isPhraseInAnyOfThesauruses(
      phraseId,
      level.thesaurusIds,
    );
    if (!isPhraseInAssociatedThesaurus) {
      throw new BadRequestException(`词组 ID "${phraseId}" 不属于此关卡所关联的任何词库。`);
    }

    if (level.phraseIds.includes(phraseId)) {
      throw new BadRequestException(`词组 ID "${phraseId}" 已存在于关卡 "${levelId}" 中。`);
    }

    level.phraseIds.push(phraseId);
    const updatedLevel = await level.save();
    return this._mapToLevelResponseDto(updatedLevel);
  }

  async removePhrase(levelId: string, phraseId: string): Promise<LevelResponseDto> {
    const level = await this.getLevelEntity(levelId);
    const phraseIndexInLevel = level.phraseIds.indexOf(phraseId);
    if (phraseIndexInLevel === -1) {
      throw new NotFoundException(`词组 ID "${phraseId}" 不在关卡 "${levelId}" 中。`);
    }

    level.phraseIds.splice(phraseIndexInLevel, 1);
    const updatedLevel = await level.save();
    return this._mapToLevelResponseDto(updatedLevel);
  }

  // 获取关卡统计信息
  async getLevelCount(): Promise<{ total: number; maxLevels: number; remaining: number }> {
    const total = await this.levelModel.countDocuments().exec();
    return {
      total,
      maxLevels: this.MAX_LEVELS,
      remaining: this.MAX_LEVELS - total,
    };
  }

  // 根据难度获取关卡
  async findByDifficulty(difficulty: number): Promise<LevelResponseDto[]> {
    const filteredLevels = await this.levelModel.find({ difficulty }).exec();
    return filteredLevels.map(level => this._mapToLevelResponseDto(level));
  }

  // 获取关卡详细信息（包含词组详情）
  async getLevelWithPhrases(levelId: string): Promise<any> {
    const level = await this.getLevelEntity(levelId);
    const phrases: any[] = [];

    for (const phraseId of level.phraseIds) {
      try {
        const phrase = await this.phraseService.findOne(phraseId);
        phrases.push(phrase);
      } catch (error) {
        // 如果词组不存在，跳过
        console.warn(`词组 ${phraseId} 不存在，已跳过`);
      }
    }

    return {
      ...this._mapToLevelResponseDto(level),
      phrases,
    };
  }
}
