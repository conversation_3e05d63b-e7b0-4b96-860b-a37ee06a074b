import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type PaymentOrderDocument = PaymentOrder & Document;

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'PENDING',     // 待支付
  SUCCESS = 'SUCCESS',     // 支付成功
  FAILED = 'FAILED',       // 支付失败
  CANCELLED = 'CANCELLED', // 已取消
  REFUNDED = 'REFUNDED',   // 已退款
}

@Schema({ timestamps: true })
export class PaymentOrder {
  @ApiProperty({ description: '订单唯一ID', example: 'pay-order-001' })
  @Prop({ required: true, unique: true })
  id: string;

  @ApiProperty({ description: '用户ID', example: '12345678' })
  @Prop({ required: true })
  userId: string;

  @ApiProperty({ description: '用户openid', example: 'wx_openid_123456' })
  @Prop({ required: true })
  openid: string;

  @ApiProperty({ description: '商户订单号', example: 'ORDER_20231201_001' })
  @Prop({ required: true, unique: true })
  out_trade_no: string;

  @ApiProperty({ description: '微信支付订单号', required: false })
  @Prop()
  transaction_id?: string;

  @ApiProperty({ description: '商品描述', example: 'VIP会员-月卡' })
  @Prop({ required: true })
  description: string;

  @ApiProperty({ description: '订单金额（分）', example: 2900 })
  @Prop({ required: true })
  total: number;

  @ApiProperty({ description: '支付状态', enum: PaymentStatus })
  @Prop({ required: true, enum: PaymentStatus, default: PaymentStatus.PENDING })
  status: PaymentStatus;

  @ApiProperty({ description: 'VIP套餐ID', example: 'vip_monthly' })
  @Prop({ required: true })
  vip_package_id: string;

  @ApiProperty({ description: '预支付ID', required: false })
  @Prop()
  prepay_id?: string;

  @ApiProperty({ description: '商品详情', required: false })
  @Prop()
  detail?: string;

  @ApiProperty({ description: '附加数据', required: false })
  @Prop()
  attach?: string;

  @ApiProperty({ description: '支付完成时间', required: false })
  @Prop()
  paid_at?: Date;

  @ApiProperty({ description: '过期时间' })
  @Prop({ required: true })
  expires_at: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const PaymentOrderSchema = SchemaFactory.createForClass(PaymentOrder);

// 创建索引
PaymentOrderSchema.index({ userId: 1 });
PaymentOrderSchema.index({ openid: 1 });
PaymentOrderSchema.index({ out_trade_no: 1 }, { unique: true });
PaymentOrderSchema.index({ transaction_id: 1 });
PaymentOrderSchema.index({ status: 1 });
PaymentOrderSchema.index({ expires_at: 1 });

// VIP套餐实体
@Schema({ timestamps: true })
export class VipPackage {
  @ApiProperty({ description: '套餐ID', example: 'vip_monthly' })
  @Prop({ required: true, unique: true })
  id: string;

  @ApiProperty({ description: '套餐名称', example: 'VIP月卡' })
  @Prop({ required: true })
  name: string;

  @ApiProperty({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡' })
  @Prop({ required: true })
  description: string;

  @ApiProperty({ description: '价格（分）', example: 2900 })
  @Prop({ required: true })
  price: number;

  @ApiProperty({ description: '有效期（天）', example: 30 })
  @Prop({ required: true })
  duration: number;

  @ApiProperty({ description: '排序权重', example: 1 })
  @Prop({ required: true, default: 1 })
  sortOrder: number;

  @ApiProperty({ description: '是否启用', example: true })
  @Prop({ required: true, default: true })
  isActive: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const VipPackageSchema = SchemaFactory.createForClass(VipPackage);
export type VipPackageDocument = VipPackage & Document;
