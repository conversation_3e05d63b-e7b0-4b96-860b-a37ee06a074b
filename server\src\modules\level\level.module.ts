import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LevelController } from './level.controller';
import { LevelService } from './level.service';
import { ThesaurusModule } from '../thesaurus/thesaurus.module';
import { PhraseModule } from '../phrase/phrase.module';
import { Level, LevelSchema } from './entities/level.entity';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Level.name, schema: LevelSchema }]),
    forwardRef(() => ThesaurusModule), // 导入 ThesaurusModule 以使用 ThesaurusService
    forwardRef(() => PhraseModule),    // 导入 PhraseModule 以使用 PhraseService
  ],
  controllers: [LevelController],
  providers: [LevelService],
  exports: [LevelService], // 导出 LevelService 供其他模块使用
})
export class LevelModule {}
