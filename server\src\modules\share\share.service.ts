import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateShareConfigDto } from './dto/create-share-config.dto';
import { UpdateShareConfigDto } from './dto/update-share-config.dto';
import { ShareConfigResponseDto } from './dto/share-config-response.dto';
import { ShareConfig, ShareConfigDocument } from './entities/share-config.entity';
import { formatDate } from '../../common/utils/date-formatter';

@Injectable()
export class ShareService {
  private readonly logger = new Logger(ShareService.name);

  constructor(
    @InjectModel(ShareConfig.name) private shareConfigModel: Model<ShareConfigDocument>,
  ) {}

  // 生成唯一的分享配置ID
  private async generateUniqueShareConfigId(): Promise<string> {
    let id: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      attempts++;
      const timestamp = Date.now().toString().slice(-6);
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      id = `share-config-${timestamp}-${random}`;

      const existingConfig = await this.shareConfigModel.findOne({ id }).exec();
      if (!existingConfig) {
        break;
      }

      if (attempts >= maxAttempts) {
        throw new BadRequestException('无法生成唯一的分享配置ID，请稍后重试');
      }
    } while (true);

    return id;
  }

  // 创建分享配置
  async create(createShareConfigDto: CreateShareConfigDto): Promise<ShareConfigResponseDto> {
    // 检查同类型的配置是否已存在
    const type = createShareConfigDto.type || 'default';
    if (type === 'default') {
      const existingDefault = await this.shareConfigModel.findOne({ type: 'default', isActive: true }).exec();
      if (existingDefault) {
        throw new BadRequestException('默认分享配置已存在，请先禁用现有配置');
      }
    }

    const newShareConfig = new this.shareConfigModel({
      id: await this.generateUniqueShareConfigId(),
      name: createShareConfigDto.name,
      title: createShareConfigDto.title,
      path: createShareConfigDto.path,
      imageUrl: createShareConfigDto.imageUrl,
      description: createShareConfigDto.description,
      type: type,
      isActive: createShareConfigDto.isActive ?? true,
      sortOrder: createShareConfigDto.sortOrder ?? 1,
    });

    const savedConfig = await newShareConfig.save();
    this.logger.log(`✅ 创建分享配置成功: ${savedConfig.id} - ${savedConfig.name}`);
    
    return this.mapToResponseDto(savedConfig);
  }

  // 获取所有分享配置
  async findAll(): Promise<ShareConfigResponseDto[]> {
    const configs = await this.shareConfigModel
      .find()
      .sort({ sortOrder: 1, createdAt: -1 })
      .exec();

    return configs.map(config => this.mapToResponseDto(config));
  }

  // 获取启用的分享配置
  async findActive(): Promise<ShareConfigResponseDto[]> {
    const configs = await this.shareConfigModel
      .find({ isActive: true })
      .sort({ sortOrder: 1, createdAt: -1 })
      .exec();

    return configs.map(config => this.mapToResponseDto(config));
  }

  // 根据ID获取分享配置
  async findOne(id: string): Promise<ShareConfigResponseDto> {
    const config = await this.getShareConfigEntity(id);
    return this.mapToResponseDto(config);
  }

  // 根据类型获取分享配置
  async findByType(type: string): Promise<ShareConfigResponseDto | null> {
    const config = await this.shareConfigModel
      .findOne({ type, isActive: true })
      .exec();

    return config ? this.mapToResponseDto(config) : null;
  }

  // 获取默认分享配置
  async getDefaultConfig(): Promise<ShareConfigResponseDto | null> {
    return this.findByType('default');
  }

  // 更新分享配置
  async update(id: string, updateShareConfigDto: UpdateShareConfigDto): Promise<ShareConfigResponseDto> {
    const existingConfig = await this.getShareConfigEntity(id);

    // 如果要更新为默认类型，检查是否已有其他默认配置
    if (updateShareConfigDto.type === 'default' && existingConfig.type !== 'default') {
      const existingDefault = await this.shareConfigModel.findOne({ 
        type: 'default', 
        isActive: true,
        id: { $ne: id }
      }).exec();
      
      if (existingDefault) {
        throw new BadRequestException('默认分享配置已存在，请先禁用现有配置');
      }
    }

    // 更新字段
    Object.assign(existingConfig, updateShareConfigDto);
    const updatedConfig = await existingConfig.save();

    this.logger.log(`📝 更新分享配置: ${updatedConfig.id} - ${updatedConfig.name}`);
    return this.mapToResponseDto(updatedConfig);
  }

  // 删除分享配置
  async remove(id: string): Promise<void> {
    const config = await this.getShareConfigEntity(id);
    
    if (config.type === 'default') {
      throw new BadRequestException('不能删除默认分享配置，请先禁用');
    }

    await this.shareConfigModel.findOneAndDelete({ id }).exec();
    this.logger.log(`🗑️ 删除分享配置: ${id} - ${config.name}`);
  }

  // 启用/禁用分享配置
  async toggleActive(id: string): Promise<ShareConfigResponseDto> {
    const config = await this.getShareConfigEntity(id);
    config.isActive = !config.isActive;
    
    const updatedConfig = await config.save();
    this.logger.log(`🔄 切换分享配置状态: ${updatedConfig.id} - ${updatedConfig.isActive ? '启用' : '禁用'}`);
    
    return this.mapToResponseDto(updatedConfig);
  }

  // 获取分享配置实体（内部方法）
  private async getShareConfigEntity(id: string): Promise<ShareConfigDocument> {
    const config = await this.shareConfigModel.findOne({ id }).exec();
    if (!config) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的分享配置`);
    }
    return config;
  }

  // 映射到响应DTO
  private mapToResponseDto(config: ShareConfigDocument): ShareConfigResponseDto {
    return {
      id: config.id,
      name: config.name,
      title: config.title,
      path: config.path,
      imageUrl: config.imageUrl,
      description: config.description,
      type: config.type,
      isActive: config.isActive,
      sortOrder: config.sortOrder,
      createdAt: formatDate(config.createdAt),
      updatedAt: formatDate(config.updatedAt),
    };
  }

  // 初始化默认分享配置
  async initializeDefaultConfig(): Promise<void> {
    const existingDefault = await this.shareConfigModel.findOne({ type: 'default' }).exec();
    
    if (!existingDefault) {
      const defaultConfig = new this.shareConfigModel({
        id: await this.generateUniqueShareConfigId(),
        name: '默认分享配置',
        title: '一起来挑战词汇游戏！',
        path: '/pages/index/index',
        description: '挑战你的词汇量，看看你能通过多少关！',
        type: 'default',
        isActive: true,
        sortOrder: 1,
      });

      await defaultConfig.save();
      this.logger.log('🎯 初始化默认分享配置完成');
    }
  }
}
