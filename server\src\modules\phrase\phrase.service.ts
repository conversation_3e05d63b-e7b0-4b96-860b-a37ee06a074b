import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreatePhraseDto } from './dto/create-phrase.dto';
import { UpdatePhraseDto } from './dto/update-phrase.dto';
import { Phrase, PhraseDocument, PhraseEntity } from './entities/phrase.entity';
import { v4 as uuidv4 } from 'uuid';
import { PhraseResponseDto } from './dto/phrase-response.dto';
import { formatDate } from '../../common/utils/date-formatter';

@Injectable()
export class PhraseService {
  constructor(
    @InjectModel(Phrase.name) private phraseModel: Model<PhraseDocument>,
  ) {}

  // 私有方法，用于根据ID查找实体，未找到则抛出 NotFoundException
  private async _findEntityById(id: string): Promise<PhraseDocument> {
    const phrase = await this.phraseModel.findOne({ id }).exec();
    if (!phrase) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的词组`);
    }
    return phrase;
  }

  private _mapToPhraseResponseDto(phrase: PhraseDocument): PhraseResponseDto {
    return {
      id: phrase.id,
      text: phrase.text,
      meaning: phrase.meaning,
      exampleSentence: phrase.exampleSentence,
      tags: phrase.tags,
      createdAt: formatDate(phrase.createdAt),
      updatedAt: formatDate(phrase.updatedAt),
    };
  }

  async create(createPhraseDto: CreatePhraseDto): Promise<PhraseResponseDto> {
    const newPhrase = new this.phraseModel({
      id: uuidv4(),
      ...createPhraseDto,
    });
    const savedPhrase = await newPhrase.save();
    return this._mapToPhraseResponseDto(savedPhrase);
  }

  async findAll(): Promise<PhraseResponseDto[]> {
    const phrases = await this.phraseModel.find().exec();
    return phrases.map(phrase => this._mapToPhraseResponseDto(phrase));
  }

  async findOne(id: string): Promise<PhraseResponseDto> {
    const phrase = await this._findEntityById(id);
    return this._mapToPhraseResponseDto(phrase);
  }

  async update(id: string, updatePhraseDto: UpdatePhraseDto): Promise<PhraseResponseDto> {
    const updatedPhrase = await this.phraseModel.findOneAndUpdate(
      { id },
      { ...updatePhraseDto, updatedAt: new Date() },
      { new: true }
    ).exec();

    if (!updatedPhrase) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的词组`);
    }

    return this._mapToPhraseResponseDto(updatedPhrase);
  }

  async remove(id: string): Promise<void> {
    const result = await this.phraseModel.deleteOne({ id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`未找到 ID 为 "${id}" 的词组`);
    }
  }
}
