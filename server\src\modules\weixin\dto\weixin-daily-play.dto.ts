import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class WeixinShareDto {
  @ApiProperty({ description: '微信用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' })
  @IsString()
  @IsNotEmpty({ message: 'openid不能为空' })
  openid: string;
}

export class WeixinShareResponseDto {
  @ApiProperty({ description: '分享状态', example: 'success' })
  status: 'success' | 'already_shared';

  @ApiProperty({ description: '响应消息', example: '分享成功，获得5次额外通关机会！' })
  message: string;

  @ApiProperty({ description: '用户ID', example: '12345678' })
  userId: string;

  @ApiProperty({ description: '当前每日解锁次数', example: 3 })
  dailyUnlockCount: number;

  @ApiProperty({ description: '每日解锁限制', example: 20 })
  dailyUnlockLimit: number;

  @ApiProperty({ description: '剩余解锁次数', example: 17 })
  remainingUnlocks: number;

  @ApiProperty({ description: '是否为VIP用户', example: false })
  isVip: boolean;

  @ApiProperty({ description: '总分享次数', example: 5 })
  totalShares: number;
}

export class WeixinDailyStatusDto {
  @ApiProperty({ description: '用户ID', example: '12345678' })
  id: string;

  @ApiProperty({ description: '当前每日解锁次数', example: 3 })
  dailyUnlockCount: number;

  @ApiProperty({ description: '每日解锁限制', example: 15 })
  dailyUnlockLimit: number;

  @ApiProperty({ description: '剩余解锁次数', example: 12 })
  remainingUnlocks: number;

  @ApiProperty({ description: '当日是否已分享', example: false })
  dailyShared: boolean;

  @ApiProperty({ description: '是否为VIP用户', example: false })
  isVip: boolean;

  @ApiProperty({ description: '最后游戏日期', example: '2025-06-19' })
  lastPlayDate: string;

  @ApiProperty({ description: '总分享次数', example: 5 })
  totalShares: number;

  @ApiProperty({ description: '是否可以解锁关卡', example: true })
  canUnlock: boolean;

  @ApiProperty({ description: '限制原因（如果不能解锁）', required: false })
  limitReason?: string;
}
