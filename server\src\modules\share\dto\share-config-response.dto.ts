import { ApiProperty } from '@nestjs/swagger';

export class ShareConfigResponseDto {
  @ApiProperty({ description: '分享配置ID', example: 'share-config-001' })
  id: string;

  @ApiProperty({ description: '分享配置名称', example: '默认分享配置' })
  name: string;

  @ApiProperty({ description: '分享标题', example: '一起来挑战词汇游戏！' })
  title: string;

  @ApiProperty({ description: '分享路径', example: '/pages/index/index' })
  path: string;

  @ApiProperty({ description: '分享图片URL', required: false })
  imageUrl?: string;

  @ApiProperty({ description: '分享描述', required: false })
  description?: string;

  @ApiProperty({ description: '分享类型', example: 'default' })
  type: string;

  @ApiProperty({ description: '是否启用', example: true })
  isActive: boolean;

  @ApiProperty({ description: '排序权重', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '创建时间', example: '2025-06-19T10:00:00.000Z' })
  createdAt: string;

  @ApiProperty({ description: '更新时间', example: '2025-06-19T12:00:00.000Z' })
  updatedAt: string;
}
