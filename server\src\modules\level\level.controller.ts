import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  BadRequestException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { LevelService } from './level.service';
import { CreateLevelDto } from './dto/create-level.dto';
import { UpdateLevelDto } from './dto/update-level.dto';
import { LevelResponseDto } from './dto/level-response.dto';
import { AddPhraseToLevelDto } from './dto/add-phrase-to-level.dto';

@ApiTags('levels') // 将此控制器下的接口归类到 'levels' 标签
@Controller('levels')
export class LevelController {
  constructor(private readonly levelService: LevelService) {}

  @Post()
  @ApiOperation({ summary: '创建新关卡' })
  @ApiResponse({ status: 200, description: '关卡创建成功（兼容旧版，实际应为201）', type: LevelResponseDto }) // 保持与之前一致，但201更合适
  @ApiResponse({ status: 201, description: '关卡创建成功', type: LevelResponseDto })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  create(@Body() createLevelDto: CreateLevelDto) {
    return this.levelService.create(createLevelDto);
  }

  @Get()
  @ApiOperation({ summary: '获取所有关卡列表' })
  @ApiResponse({ status: 200, description: '成功获取关卡列表', type: [LevelResponseDto] })
  findAll() {
    return this.levelService.findAll();
  }

  @Get('count')
  @ApiOperation({ summary: '获取关卡总数' })
  @ApiResponse({
    status: 200,
    description: '成功获取关卡总数',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number', description: '关卡总数' },
        maxLevels: { type: 'number', description: '最大关卡数限制' },
        remaining: { type: 'number', description: '剩余可创建关卡数' },
      }
    }
  })
  getLevelCount() {
    return this.levelService.getLevelCount();
  }

  @Get('difficulty/:difficulty')
  @ApiOperation({ summary: '根据难度获取关卡列表' })
  @ApiParam({ name: 'difficulty', description: '关卡难度 (1-5)' })
  @ApiResponse({ status: 200, description: '成功获取指定难度的关卡列表', type: [LevelResponseDto] })
  @ApiResponse({ status: 400, description: '难度参数无效' })
  findByDifficulty(@Param('difficulty') difficulty: string) {
    const difficultyNum = parseInt(difficulty, 10);
    if (isNaN(difficultyNum) || difficultyNum < 1 || difficultyNum > 5) {
      throw new BadRequestException('难度必须是1-5之间的数字');
    }
    return this.levelService.findByDifficulty(difficultyNum);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取单个关卡' })
  @ApiParam({ name: 'id', description: '关卡的UUID' })
  @ApiResponse({ status: 200, description: '成功获取关卡', type: LevelResponseDto })
  @ApiResponse({ status: 404, description: '关卡未找到' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.levelService.findOne(id);
  }

  @Get(':id/with-phrases')
  @ApiOperation({ summary: '获取关卡详细信息（包含词组详情）' })
  @ApiParam({ name: 'id', description: '关卡的UUID' })
  @ApiResponse({
    status: 200,
    description: '成功获取关卡详细信息',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        difficulty: { type: 'number' },
        description: { type: 'string' },
        thesaurusIds: { type: 'array', items: { type: 'string' } },
        phraseIds: { type: 'array', items: { type: 'string' } },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' },
        phrases: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              text: { type: 'string' },
              meaning: { type: 'string' },
              example: { type: 'string' },
              tags: { type: 'array', items: { type: 'string' } },
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '关卡未找到' })
  findOneWithPhrases(@Param('id', ParseUUIDPipe) id: string) {
    return this.levelService.getLevelWithPhrases(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新指定ID的关卡' })
  @ApiParam({ name: 'id', description: '关卡的UUID' })
  @ApiResponse({ status: 200, description: '关卡更新成功', type: LevelResponseDto })
  @ApiResponse({ status: 404, description: '关卡未找到' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  update(@Param('id', ParseUUIDPipe) id: string, @Body() updateLevelDto: UpdateLevelDto) {
    return this.levelService.update(id, updateLevelDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除指定ID的关卡' })
  @ApiParam({ name: 'id', description: '关卡的UUID' })
  @ApiResponse({ status: 204, description: '关卡删除成功' })
  @ApiResponse({ status: 404, description: '关卡未找到' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.levelService.remove(id);
  }

  // --- 关卡与词组关联接口 ---
  @Post(':id/phrases')
  @ApiOperation({ summary: '向指定关卡添加词组' })
  @ApiParam({ name: 'id', description: '关卡的UUID' })
  @ApiResponse({ status: 200, description: '词组添加成功', type: LevelResponseDto })
  @ApiResponse({ status: 404, description: '关卡或词组未找到' })
  @ApiResponse({ status: 400, description: '请求参数错误、词组不属于关联词库或词组已存在于关卡中' })
  addPhraseToLevel(
    @Param('id', ParseUUIDPipe) levelId: string,
    @Body() addPhraseDto: AddPhraseToLevelDto,
  ) {
    return this.levelService.addPhrase(levelId, addPhraseDto.phraseId);
  }

  @Delete(':id/phrases/:phraseId')
  @ApiOperation({ summary: '从指定关卡移除词组' })
  @ApiParam({ name: 'id', description: '关卡的UUID' })
  @ApiParam({ name: 'phraseId', description: '词组的UUID' })
  @ApiResponse({ status: 200, description: '词组移除成功', type: LevelResponseDto })
  @ApiResponse({ status: 404, description: '关卡或词组未在关卡中找到' })
  removePhraseFromLevel(
    @Param('id', ParseUUIDPipe) levelId: string,
    @Param('phraseId', ParseUUIDPipe) phraseId: string,
  ) {
    return this.levelService.removePhrase(levelId, phraseId);
  }


}
