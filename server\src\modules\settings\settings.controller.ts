import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { SettingsService } from './settings.service';
import { UpdateSettingsDto } from './dto/update-settings.dto';
import { SettingsResponseDto } from './dto/settings-response.dto';

@ApiTags('settings') // 将此控制器下的接口归类到 'settings' 标签
@Controller('settings')
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}



  @Get()
  @ApiOperation({ summary: '获取所有设置列表' })
  @ApiResponse({ status: 200, description: '成功获取设置列表', type: [SettingsResponseDto] })
  findAll() {
    return this.settingsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取设置详情' })
  @ApiParam({ name: 'id', description: '设置ID' })
  @ApiResponse({ status: 200, description: '成功获取设置详情', type: SettingsResponseDto })
  @ApiResponse({ status: 404, description: '设置不存在' })
  findOne(@Param('id') id: string) {
    return this.settingsService.findOne(id);
  }

  @Get('key/:key')
  @ApiOperation({ summary: '根据键名获取设置' })
  @ApiParam({ name: 'key', description: '设置键名' })
  @ApiResponse({ status: 200, description: '成功获取设置', type: SettingsResponseDto })
  @ApiResponse({ status: 404, description: '设置不存在' })
  findByKey(@Param('key') key: string) {
    return this.settingsService.findByKey(key);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新设置' })
  @ApiParam({ name: 'id', description: '设置ID' })
  @ApiResponse({ status: 200, description: '设置更新成功', type: SettingsResponseDto })
  @ApiResponse({ status: 404, description: '设置不存在' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  update(@Param('id') id: string, @Body() updateSettingsDto: UpdateSettingsDto) {
    return this.settingsService.update(id, updateSettingsDto);
  }

  @Patch('key/:key')
  @ApiOperation({ summary: '根据键名更新设置值' })
  @ApiParam({ name: 'key', description: '设置键名' })
  @ApiResponse({ status: 200, description: '设置更新成功', type: SettingsResponseDto })
  @ApiResponse({ status: 404, description: '设置不存在' })
  updateByKey(@Param('key') key: string, @Body() body: { value: string }) {
    return this.settingsService.updateByKey(key, body.value);
  }



  @Post('initialize')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '初始化默认设置' })
  @ApiResponse({ status: 200, description: '默认设置初始化成功' })
  initializeDefaults() {
    return this.settingsService.initializeDefaultSettings();
  }
}
