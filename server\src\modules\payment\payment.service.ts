import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PaymentOrder, PaymentOrderDocument, PaymentStatus, VipPackage, VipPackageDocument } from './entities/payment-order.entity';
import { WechatPayService } from './services/wechat-pay.service';
import { UserService } from '../user/user.service';
import { CreatePaymentDto, MiniProgramPaymentResponse, VipPackageDto, CreateVipPackageDto, UpdateVipPackageDto } from './dto/payment.dto';
import { formatDate } from '../../common/utils/date-formatter';
import { generateSemanticVipPackageId, generateUniqueId } from '../../common/utils/id-generator';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  // 支付订单配置常量
  private readonly PAYMENT_ORDER_EXPIRE_MINUTES = 30; // 支付订单有效期（分钟）
  private readonly PAYMENT_ORDER_EXPIRE_MS = this.PAYMENT_ORDER_EXPIRE_MINUTES * 60 * 1000; // 转换为毫秒

  constructor(
    @InjectModel(PaymentOrder.name) private paymentOrderModel: Model<PaymentOrderDocument>,
    @InjectModel(VipPackage.name) private vipPackageModel: Model<VipPackageDocument>,
    private wechatPayService: WechatPayService,
    private userService: UserService,
  ) {}

  // 生成唯一订单号
  private generateOrderNo(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `ORDER_${timestamp}_${random}`;
  }

  // 获取支付订单过期时间配置
  getPaymentOrderExpireMinutes(): number {
    return this.PAYMENT_ORDER_EXPIRE_MINUTES;
  }

  // 检查并处理用户未完成订单
  private async checkAndHandleExistingOrder(
    userId: string,
    packageId: string,
    openid: string
  ): Promise<MiniProgramPaymentResponse | null> {
    const existingOrder = await this.paymentOrderModel.findOne({
      userId: userId,
      status: PaymentStatus.PENDING,
      expires_at: { $gt: new Date() }
    }).exec();

    if (!existingOrder) {
      return null; // 没有未完成订单
    }

    this.logger.log(`发现用户${userId}的未完成订单: ${existingOrder.out_trade_no}`);

    // 检查微信支付状态
    try {
      const wechatOrder = await this.wechatPayService.queryOrder(existingOrder.out_trade_no);

      if (wechatOrder) {
        // 同步微信支付状态
        await this.syncWechatOrderStatus(existingOrder, wechatOrder);

        // 如果订单已支付，返回null让调用方创建新订单
        if (existingOrder.status !== PaymentStatus.PENDING) {
          return null;
        }
      }
    } catch (error) {
      this.logger.warn(`查询微信订单状态失败: ${existingOrder.out_trade_no}`, error.message);
    }

    // 如果是相同套餐且有prepay_id，重新返回支付参数
    if (existingOrder.vip_package_id === packageId && existingOrder.prepay_id) {
      this.logger.log(`重新返回相同套餐的支付参数: ${existingOrder.out_trade_no}`);

      try {
        const payParams = this.wechatPayService.generateMiniProgramPayParams(existingOrder.prepay_id);
        return payParams;
      } catch (error) {
        this.logger.warn(`重新生成支付参数失败，将创建新订单: ${error.message}`);
        // 标记旧订单为失败
        existingOrder.status = PaymentStatus.FAILED;
        await existingOrder.save();
        return null;
      }
    }

    // 如果是不同套餐，取消旧订单
    if (existingOrder.vip_package_id !== packageId) {
      this.logger.log(`取消不同套餐的旧订单: ${existingOrder.out_trade_no}`);
      existingOrder.status = PaymentStatus.CANCELLED;
      await existingOrder.save();
      return null;
    }

    // 如果没有prepay_id，说明订单创建不完整，取消它
    if (!existingOrder.prepay_id) {
      this.logger.log(`取消没有prepay_id的不完整订单: ${existingOrder.out_trade_no}`);
      existingOrder.status = PaymentStatus.CANCELLED;
      await existingOrder.save();
      return null;
    }

    return null;
  }

  // 同步微信订单状态
  private async syncWechatOrderStatus(paymentOrder: PaymentOrderDocument, wechatOrder: any): Promise<void> {
    const oldStatus = paymentOrder.status;

    switch (wechatOrder.trade_state) {
      case 'SUCCESS':
        paymentOrder.status = PaymentStatus.SUCCESS;
        paymentOrder.transaction_id = wechatOrder.transaction_id;
        paymentOrder.paid_at = new Date(wechatOrder.success_time);

        // 激活用户VIP
        try {
          await this.activateUserVip(paymentOrder.userId, paymentOrder.vip_package_id);
          this.logger.log(`用户VIP激活成功: userId=${paymentOrder.userId}, packageId=${paymentOrder.vip_package_id}`);
        } catch (error) {
          this.logger.error('激活用户VIP失败:', error);
        }
        break;

      case 'REFUND':
        paymentOrder.status = PaymentStatus.REFUNDED;
        break;

      case 'NOTPAY':
      case 'USERPAYING':
        // 保持待支付状态
        break;

      case 'CLOSED':
      case 'REVOKED':
      case 'PAYERROR':
        paymentOrder.status = PaymentStatus.FAILED;
        break;

      default:
        this.logger.warn(`未知的微信支付状态: ${wechatOrder.trade_state}`);
        break;
    }

    if (oldStatus !== paymentOrder.status) {
      await paymentOrder.save();
      this.logger.log(`订单状态已同步: ${paymentOrder.out_trade_no} ${oldStatus} -> ${paymentOrder.status}`);
    }
  }

  // 生成唯一支付订单ID
  private async generateUniquePaymentOrderId(): Promise<string> {
    let id: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      attempts++;
      const timestamp = Date.now().toString().slice(-8);
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      id = `pay-order-${timestamp}-${random}`;

      const existingOrder = await this.paymentOrderModel.findOne({ id }).exec();
      if (!existingOrder) {
        break;
      }

      if (attempts >= maxAttempts) {
        throw new BadRequestException('无法生成唯一的支付订单ID，请稍后重试');
      }
    } while (true);

    return id;
  }

  // 获取VIP套餐列表
  async getVipPackages(): Promise<VipPackageDto[]> {
    const packages = await this.vipPackageModel
      .find({ isActive: true })
      .sort({ sortOrder: 1 })
      .exec();

    return packages.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      price: pkg.price,
      duration: pkg.duration,
      sortOrder: pkg.sortOrder,
      isActive: pkg.isActive,
      createdAt: formatDate(pkg.createdAt),
      updatedAt: formatDate(pkg.updatedAt),
    }));
  }

  // 获取所有VIP套餐列表（包括禁用的）
  async getAllVipPackages(): Promise<VipPackageDto[]> {
    const packages = await this.vipPackageModel
      .find({})
      .sort({ sortOrder: 1 })
      .exec();

    return packages.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      price: pkg.price,
      duration: pkg.duration,
      sortOrder: pkg.sortOrder,
      isActive: pkg.isActive,
      createdAt: formatDate(pkg.createdAt),
      updatedAt: formatDate(pkg.updatedAt),
    }));
  }

  // 创建VIP套餐
  async createVipPackage(createDto: CreateVipPackageDto): Promise<VipPackageDto> {
    // 生成唯一的套餐ID
    const packageId = await generateUniqueId(
      async (id: string) => {
        const existing = await this.vipPackageModel.findOne({ id }).exec();
        return !!existing;
      },
      () => generateSemanticVipPackageId(createDto.name, createDto.duration),
      10
    );

    // 如果没有指定排序权重，使用最大值+1
    let sortOrder = createDto.sortOrder;
    if (!sortOrder) {
      const maxSortOrder = await this.vipPackageModel
        .findOne({})
        .sort({ sortOrder: -1 })
        .exec();
      sortOrder = maxSortOrder ? maxSortOrder.sortOrder + 1 : 1;
    }

    const newPackage = new this.vipPackageModel({
      id: packageId,
      name: createDto.name,
      description: createDto.description,
      price: createDto.price,
      duration: createDto.duration,
      sortOrder,
      isActive: createDto.isActive !== undefined ? createDto.isActive : true,
    });

    const savedPackage = await newPackage.save();

    this.logger.log(`创建VIP套餐成功: ${savedPackage.id} - ${savedPackage.name}`);

    return {
      id: savedPackage.id,
      name: savedPackage.name,
      description: savedPackage.description,
      price: savedPackage.price,
      duration: savedPackage.duration,
      sortOrder: savedPackage.sortOrder,
      isActive: savedPackage.isActive,
      createdAt: formatDate(savedPackage.createdAt),
      updatedAt: formatDate(savedPackage.updatedAt),
    };
  }

  // 更新VIP套餐
  async updateVipPackage(id: string, updateDto: UpdateVipPackageDto): Promise<VipPackageDto> {
    const existingPackage = await this.vipPackageModel.findOne({ id }).exec();
    if (!existingPackage) {
      throw new NotFoundException(`VIP套餐 "${id}" 不存在`);
    }

    // 更新字段
    if (updateDto.name !== undefined) existingPackage.name = updateDto.name;
    if (updateDto.description !== undefined) existingPackage.description = updateDto.description;
    if (updateDto.price !== undefined) existingPackage.price = updateDto.price;
    if (updateDto.duration !== undefined) existingPackage.duration = updateDto.duration;
    if (updateDto.sortOrder !== undefined) existingPackage.sortOrder = updateDto.sortOrder;
    if (updateDto.isActive !== undefined) existingPackage.isActive = updateDto.isActive;

    const updatedPackage = await existingPackage.save();

    this.logger.log(`更新VIP套餐成功: ${updatedPackage.id} - ${updatedPackage.name}`);

    return {
      id: updatedPackage.id,
      name: updatedPackage.name,
      description: updatedPackage.description,
      price: updatedPackage.price,
      duration: updatedPackage.duration,
      sortOrder: updatedPackage.sortOrder,
      isActive: updatedPackage.isActive,
      createdAt: formatDate(updatedPackage.createdAt),
      updatedAt: formatDate(updatedPackage.updatedAt),
    };
  }

  // 删除VIP套餐
  async deleteVipPackage(id: string): Promise<void> {
    const existingPackage = await this.vipPackageModel.findOne({ id }).exec();
    if (!existingPackage) {
      throw new NotFoundException(`VIP套餐 "${id}" 不存在`);
    }

    // 检查是否有相关的支付订单
    const relatedOrders = await this.paymentOrderModel.findOne({ vip_package_id: id }).exec();
    if (relatedOrders) {
      throw new BadRequestException(`无法删除套餐 "${id}"，存在相关的支付订单`);
    }

    await this.vipPackageModel.deleteOne({ id }).exec();

    this.logger.log(`删除VIP套餐成功: ${id}`);
  }

  // 根据ID获取VIP套餐
  async getVipPackageById(id: string): Promise<VipPackageDto> {
    const vipPackage = await this.vipPackageModel.findOne({ id }).exec();
    if (!vipPackage) {
      throw new NotFoundException(`VIP套餐 "${id}" 不存在`);
    }

    return {
      id: vipPackage.id,
      name: vipPackage.name,
      description: vipPackage.description,
      price: vipPackage.price,
      duration: vipPackage.duration,
      sortOrder: vipPackage.sortOrder,
      isActive: vipPackage.isActive,
      createdAt: formatDate(vipPackage.createdAt),
      updatedAt: formatDate(vipPackage.updatedAt),
    };
  }

  // 根据ID获取VIP套餐（内部使用，返回Document）
  private async getVipPackageDocument(packageId: string): Promise<VipPackageDocument> {
    const vipPackage = await this.vipPackageModel.findOne({ id: packageId, isActive: true }).exec();
    if (!vipPackage) {
      throw new NotFoundException(`VIP套餐 "${packageId}" 不存在或已下架`);
    }
    return vipPackage;
  }

  // 创建支付订单
  async createPaymentOrder(openid: string, packageId: string): Promise<MiniProgramPaymentResponse> {
    // 获取用户信息
    const user = await this.userService.getUserByOpenid(openid);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 获取VIP套餐信息
    const vipPackage = await this.getVipPackageDocument(packageId);

    // 检查并处理未完成的订单
    const existingPayParams = await this.checkAndHandleExistingOrder(user.id, packageId, openid);
    if (existingPayParams) {
      return existingPayParams;
    }

    // 生成订单号
    const out_trade_no = this.generateOrderNo();
    const orderId = await this.generateUniquePaymentOrderId();

    // 创建支付订单记录
    const paymentOrder = new this.paymentOrderModel({
      id: orderId,
      userId: user.id,
      openid: openid,
      out_trade_no: out_trade_no,
      description: vipPackage.name,
      total: vipPackage.price,
      status: PaymentStatus.PENDING,
      vip_package_id: packageId,
      detail: vipPackage.description,
      attach: JSON.stringify({ userId: user.id, packageId }),
      expires_at: new Date(Date.now() + this.PAYMENT_ORDER_EXPIRE_MS), // 支付订单过期时间
    });

    try {
      // 调用微信支付统一下单
      const wechatResponse = await this.wechatPayService.createOrder({
        description: vipPackage.name,
        out_trade_no: out_trade_no,
        total: vipPackage.price,
        openid: openid,
        detail: vipPackage.description,
        attach: JSON.stringify({ userId: user.id, packageId }),
        expireMinutes: this.PAYMENT_ORDER_EXPIRE_MINUTES, // 使用配置的过期时间
      });

      // 更新订单的prepay_id
      paymentOrder.prepay_id = wechatResponse.prepay_id;
      await paymentOrder.save();

      // 生成小程序支付参数
      const payParams = this.wechatPayService.generateMiniProgramPayParams(wechatResponse.prepay_id);

      this.logger.log(`创建支付订单成功: userId=${user.id}, orderId=${orderId}, amount=${vipPackage.price}`);

      return payParams;
    } catch (error) {
      this.logger.error('创建支付订单失败:', error);
      throw new BadRequestException('创建支付订单失败，请稍后重试');
    }
  }

  // 处理支付回调 - 优化回调处理逻辑和幂等性
  async handlePaymentNotify(notifyData: any): Promise<void> {
    const { out_trade_no, transaction_id, trade_state, success_time, payer, amount } = notifyData;

    this.logger.log('处理支付回调开始', {
      out_trade_no,
      transaction_id,
      trade_state,
      openid: payer?.openid
    });

    try {
      // 查找支付订单
      const paymentOrder = await this.paymentOrderModel.findOne({ out_trade_no }).exec();
      if (!paymentOrder) {
        this.logger.error(`支付回调：订单不存在 ${out_trade_no}`);
        throw new Error(`订单不存在: ${out_trade_no}`);
      }

      // 验证订单金额
      if (amount && amount.total !== paymentOrder.total) {
        this.logger.error(`支付回调：订单金额不匹配`, {
          out_trade_no,
          expected: paymentOrder.total,
          actual: amount.total
        });
        throw new Error(`订单金额不匹配: ${out_trade_no}`);
      }

      // 验证用户openid
      if (payer?.openid && payer.openid !== paymentOrder.openid) {
        this.logger.error(`支付回调：用户openid不匹配`, {
          out_trade_no,
          expected: paymentOrder.openid,
          actual: payer.openid
        });
        throw new Error(`用户openid不匹配: ${out_trade_no}`);
      }

      // 幂等性检查：如果订单已经是成功状态，直接返回
      if (paymentOrder.status === PaymentStatus.SUCCESS) {
        this.logger.warn(`支付回调：订单已处理 ${out_trade_no}, 当前状态: ${paymentOrder.status}`);
        return;
      }

      // 只处理待支付状态的订单
      if (paymentOrder.status !== PaymentStatus.PENDING) {
        this.logger.warn(`支付回调：订单状态异常 ${out_trade_no}, 当前状态: ${paymentOrder.status}`);
        return;
      }

      // 根据支付状态更新订单
      switch (trade_state) {
        case 'SUCCESS':
          paymentOrder.status = PaymentStatus.SUCCESS;
          paymentOrder.transaction_id = transaction_id;
          paymentOrder.paid_at = new Date(success_time);
          await paymentOrder.save();

          // 为用户开通VIP
          await this.activateUserVip(paymentOrder.userId, paymentOrder.vip_package_id);

          this.logger.log(`支付成功处理完成`, {
            userId: paymentOrder.userId,
            orderId: paymentOrder.id,
            amount: paymentOrder.total,
            transaction_id
          });
          break;

        case 'CLOSED':
        case 'PAYERROR':
        case 'REVOKED':
          paymentOrder.status = PaymentStatus.FAILED;
          await paymentOrder.save();

          this.logger.log(`支付失败处理完成`, {
            userId: paymentOrder.userId,
            orderId: paymentOrder.id,
            trade_state
          });
          break;

        default:
          this.logger.warn(`未处理的支付状态: ${trade_state}`, { out_trade_no });
      }
    } catch (error) {
      this.logger.error('处理支付回调失败:', {
        out_trade_no,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  // 激活用户VIP
  private async activateUserVip(userId: string, packageId: string): Promise<void> {
    try {
      // 获取VIP套餐信息
      const vipPackage = await this.getVipPackageDocument(packageId);

      // 计算VIP过期时间
      const now = new Date();
      const vipExpiresAt = new Date(now.getTime() + vipPackage.duration * 24 * 60 * 60 * 1000);

      // 更新用户VIP状态
      await this.userService.update(userId, {
        isVip: true,
        vipExpiresAt,
        dailyUnlockLimit: 999999, // VIP用户设置一个很大的数字表示无限制
      });

      this.logger.log(`用户VIP激活成功: userId=${userId}, package=${packageId}, duration=${vipPackage.duration}天, 过期时间=${vipExpiresAt.toISOString()}`);
    } catch (error) {
      this.logger.error('激活用户VIP失败:', error);
      throw error;
    }
  }

  // 查询支付订单 - 优化查询逻辑和状态同步
  async queryPaymentOrder(out_trade_no: string): Promise<any> {
    const paymentOrder = await this.paymentOrderModel.findOne({ out_trade_no }).exec();
    if (!paymentOrder) {
      throw new NotFoundException('支付订单不存在');
    }

    // 如果订单状态为待支付且未过期，查询微信支付状态进行同步
    if (paymentOrder.status === PaymentStatus.PENDING && paymentOrder.expires_at > new Date()) {
      try {
        const wechatOrder = await this.wechatPayService.queryOrder(out_trade_no);

        if (wechatOrder) {
          // 根据微信支付状态更新本地订单状态
          switch (wechatOrder.trade_state) {
            case 'SUCCESS':
              paymentOrder.status = PaymentStatus.SUCCESS;
              paymentOrder.transaction_id = wechatOrder.transaction_id;
              paymentOrder.paid_at = new Date(wechatOrder.success_time);
              await paymentOrder.save();

              // 激活用户VIP
              await this.activateUserVip(paymentOrder.userId, paymentOrder.vip_package_id);
              this.logger.log(`订单状态同步成功: ${out_trade_no} -> SUCCESS`);
              break;

            case 'CLOSED':
            case 'PAYERROR':
            case 'REVOKED':
              paymentOrder.status = PaymentStatus.FAILED;
              await paymentOrder.save();
              this.logger.log(`订单状态同步成功: ${out_trade_no} -> FAILED`);
              break;

            case 'USERPAYING':
            case 'NOTPAY':
              // 用户支付中或未支付，保持待支付状态
              this.logger.debug(`订单仍在支付中: ${out_trade_no} -> ${wechatOrder.trade_state}`);
              break;

            default:
              this.logger.warn(`未知的微信支付状态: ${wechatOrder.trade_state}`);
          }
        }
      } catch (error) {
        this.logger.error('查询微信支付订单失败:', error);
        // 查询失败不影响返回本地订单状态
      }
    } else if (paymentOrder.status === PaymentStatus.PENDING && paymentOrder.expires_at <= new Date()) {
      // 订单已过期，更新为失败状态
      paymentOrder.status = PaymentStatus.FAILED;
      await paymentOrder.save();
      this.logger.log(`订单已过期，更新状态: ${out_trade_no} -> FAILED`);
    }

    return {
      id: paymentOrder.id,
      out_trade_no: paymentOrder.out_trade_no,
      transaction_id: paymentOrder.transaction_id,
      description: paymentOrder.description,
      total: paymentOrder.total,
      status: paymentOrder.status,
      vip_package_id: paymentOrder.vip_package_id,
      paid_at: paymentOrder.paid_at ? formatDate(paymentOrder.paid_at) : null,
      expires_at: formatDate(paymentOrder.expires_at),
      created_at: formatDate(paymentOrder.createdAt),
    };
  }

  // 获取支付订单列表（管理员）
  async getPaymentOrdersList(params: {
    page: number;
    pageSize: number;
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    userId?: string;
  }): Promise<any> {
    const { page, pageSize, search, status, startDate, endDate, userId } = params;

    // 构建查询条件
    const query: any = {};

    // 用户ID筛选
    if (userId) {
      query.userId = userId;
    }

    // 状态筛选
    if (status) {
      query.status = status;
    }

    // 日期范围筛选
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate + ' 23:59:59');
      }
    }

    // 搜索条件（订单号、用户ID、描述）
    if (search) {
      query.$or = [
        { out_trade_no: { $regex: search, $options: 'i' } },
        { transaction_id: { $regex: search, $options: 'i' } },
        { userId: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { openid: { $regex: search, $options: 'i' } },
      ];
    }

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 查询订单
    const [orders, total] = await Promise.all([
      this.paymentOrderModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(pageSize)
        .exec(),
      this.paymentOrderModel.countDocuments(query).exec(),
    ]);

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      id: order.id,
      userId: order.userId,
      openid: order.openid,
      out_trade_no: order.out_trade_no,
      transaction_id: order.transaction_id,
      description: order.description,
      total: order.total,
      status: order.status,
      vip_package_id: order.vip_package_id,
      paid_at: order.paid_at ? formatDate(order.paid_at) : null,
      expires_at: formatDate(order.expires_at),
      created_at: formatDate(order.createdAt),
      updated_at: formatDate(order.updatedAt),
    }));

    return {
      orders: formattedOrders,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  // 获取用户支付订单列表
  async getUserPaymentOrders(userId: string): Promise<any[]> {
    const orders = await this.paymentOrderModel
      .find({ userId })
      .sort({ createdAt: -1 })
      .exec();

    return orders.map(order => ({
      id: order.id,
      out_trade_no: order.out_trade_no,
      transaction_id: order.transaction_id,
      description: order.description,
      total: order.total,
      status: order.status,
      vip_package_id: order.vip_package_id,
      paid_at: order.paid_at ? formatDate(order.paid_at) : null,
      created_at: formatDate(order.createdAt),
    }));
  }

  // 获取支付订单统计信息
  async getPaymentOrderStats(params: {
    startDate?: string;
    endDate?: string;
  }): Promise<any> {
    const { startDate, endDate } = params;

    // 构建查询条件
    const query: any = {};
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate + ' 23:59:59');
      }
    }

    // 聚合查询统计数据
    const stats = await this.paymentOrderModel.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          pending: { $sum: { $cond: [{ $eq: ['$status', 'PENDING'] }, 1, 0] } },
          success: { $sum: { $cond: [{ $eq: ['$status', 'SUCCESS'] }, 1, 0] } },
          failed: { $sum: { $cond: [{ $eq: ['$status', 'FAILED'] }, 1, 0] } },
          cancelled: { $sum: { $cond: [{ $eq: ['$status', 'CANCELLED'] }, 1, 0] } },
          refunded: { $sum: { $cond: [{ $eq: ['$status', 'REFUNDED'] }, 1, 0] } },
          totalAmount: { $sum: '$total' },
          successAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'SUCCESS'] }, '$total', 0]
            }
          },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'PENDING'] }, '$total', 0]
            }
          },
        }
      }
    ]).exec();

    const result = stats[0] || {
      total: 0,
      pending: 0,
      success: 0,
      failed: 0,
      cancelled: 0,
      refunded: 0,
      totalAmount: 0,
      successAmount: 0,
      pendingAmount: 0,
    };

    // 计算成功率和平均订单金额
    result.successRate = result.total > 0 ? (result.success / result.total * 100) : 0;
    result.avgOrderAmount = result.total > 0 ? (result.totalAmount / result.total) : 0;

    // 保留两位小数
    result.successRate = Math.round(result.successRate * 100) / 100;
    result.avgOrderAmount = Math.round(result.avgOrderAmount * 100) / 100;

    return result;
  }

  // 根据ID获取支付订单详情
  async getPaymentOrderById(id: string): Promise<any> {
    const order = await this.paymentOrderModel.findOne({ id }).exec();
    if (!order) {
      throw new NotFoundException('支付订单不存在');
    }

    return {
      id: order.id,
      userId: order.userId,
      openid: order.openid,
      out_trade_no: order.out_trade_no,
      transaction_id: order.transaction_id,
      description: order.description,
      total: order.total,
      status: order.status,
      vip_package_id: order.vip_package_id,
      prepay_id: order.prepay_id,
      detail: order.detail,
      attach: order.attach,
      paid_at: order.paid_at ? formatDate(order.paid_at) : null,
      expires_at: formatDate(order.expires_at),
      created_at: formatDate(order.createdAt),
      updated_at: formatDate(order.updatedAt),
    };
  }

  // 手动刷新支付订单状态
  async refreshPaymentOrderStatus(out_trade_no: string): Promise<any> {
    const paymentOrder = await this.paymentOrderModel.findOne({ out_trade_no }).exec();
    if (!paymentOrder) {
      throw new NotFoundException('支付订单不存在');
    }

    const oldStatus = paymentOrder.status;
    let wechatStatus = null;
    let synced = false;

    try {
      // 查询微信支付状态
      const wechatOrder = await this.wechatPayService.queryOrder(out_trade_no);

      if (wechatOrder) {
        wechatStatus = wechatOrder.trade_state;

        // 同步微信支付状态
        await this.syncWechatOrderStatus(paymentOrder, wechatOrder);

        synced = oldStatus !== paymentOrder.status;

        this.logger.log(`手动刷新订单状态: ${out_trade_no} ${oldStatus} -> ${paymentOrder.status}`);
      } else {
        this.logger.warn(`微信订单不存在: ${out_trade_no}`);
      }
    } catch (error) {
      this.logger.error(`刷新订单状态失败: ${out_trade_no}`, error.message);
      throw new BadRequestException(`刷新订单状态失败: ${error.message}`);
    }

    return {
      message: synced ? '订单状态已刷新' : '订单状态无变化',
      out_trade_no,
      localStatus: paymentOrder.status,
      wechatStatus,
      synced,
      oldStatus,
      transaction_id: paymentOrder.transaction_id,
      paid_at: paymentOrder.paid_at ? formatDate(paymentOrder.paid_at) : null,
    };
  }

  // 取消支付订单
  async cancelPaymentOrder(out_trade_no: string, userId: string): Promise<void> {
    const paymentOrder = await this.paymentOrderModel.findOne({
      out_trade_no,
      userId,
      status: PaymentStatus.PENDING
    }).exec();

    if (!paymentOrder) {
      throw new NotFoundException('支付订单不存在或无法取消');
    }

    paymentOrder.status = PaymentStatus.CANCELLED;
    await paymentOrder.save();

    this.logger.log(`取消支付订单: userId=${userId}, orderId=${paymentOrder.id}`);
  }
}
