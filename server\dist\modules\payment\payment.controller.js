"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_service_1 = require("./payment.service");
const wechat_pay_service_1 = require("./services/wechat-pay.service");
const payment_dto_1 = require("./dto/payment.dto");
let PaymentController = PaymentController_1 = class PaymentController {
    paymentService;
    wechatPayService;
    logger = new common_1.Logger(PaymentController_1.name);
    constructor(paymentService, wechatPayService) {
        this.paymentService = paymentService;
        this.wechatPayService = wechatPayService;
    }
    async getWechatPayConfig() {
        return this.wechatPayService.getConfigStatus();
    }
    async getVipPackages() {
        return this.paymentService.getAllVipPackages();
    }
    async getVipPackageById(id) {
        return this.paymentService.getVipPackageById(id);
    }
    async createVipPackage(createDto) {
        return this.paymentService.createVipPackage(createDto);
    }
    async updateVipPackage(id, updateDto) {
        return this.paymentService.updateVipPackage(id, updateDto);
    }
    async deleteVipPackage(id) {
        await this.paymentService.deleteVipPackage(id);
        return { message: 'VIP套餐删除成功' };
    }
    async createPaymentOrder(body) {
        const { openid, packageId } = body;
        if (!openid || !packageId) {
            throw new common_1.BadRequestException('openid和packageId不能为空');
        }
        return this.paymentService.createPaymentOrder(openid, packageId);
    }
    async handlePaymentNotify(headers, body) {
        try {
            this.logger.log('收到微信支付回调通知');
            const bodyStr = JSON.stringify(body);
            const isValidSignature = this.wechatPayService.verifyNotifySignature(headers, bodyStr);
            if (!isValidSignature) {
                this.logger.error('微信支付回调签名验证失败');
                throw new common_1.BadRequestException('签名验证失败');
            }
            const decryptedData = this.wechatPayService.decryptNotifyData(body.resource);
            await this.paymentService.handlePaymentNotify(decryptedData);
            return { code: 'SUCCESS', message: '处理成功' };
        }
        catch (error) {
            this.logger.error('处理微信支付回调失败:', error);
            return { code: 'FAIL', message: '处理失败' };
        }
    }
    async queryPaymentOrder(out_trade_no) {
        return this.paymentService.queryPaymentOrder(out_trade_no);
    }
    async refreshPaymentOrderStatus(out_trade_no) {
        return this.paymentService.refreshPaymentOrderStatus(out_trade_no);
    }
    async getPaymentOrders(page = '1', pageSize = '20', search, status, startDate, endDate, userId) {
        const pageNum = parseInt(page, 10);
        const pageSizeNum = parseInt(pageSize, 10);
        return this.paymentService.getPaymentOrdersList({
            page: pageNum,
            pageSize: pageSizeNum,
            search,
            status,
            startDate,
            endDate,
            userId,
        });
    }
    async getUserPaymentOrders(userId) {
        return this.paymentService.getUserPaymentOrders(userId);
    }
    async getPaymentOrderStats(startDate, endDate) {
        return this.paymentService.getPaymentOrderStats({ startDate, endDate });
    }
    async getPaymentOrderById(id) {
        return this.paymentService.getPaymentOrderById(id);
    }
    async cancelPaymentOrder(out_trade_no, body) {
        const { userId } = body;
        if (!userId) {
            throw new common_1.BadRequestException('userId不能为空');
        }
        await this.paymentService.cancelPaymentOrder(out_trade_no, userId);
        return { message: '订单取消成功' };
    }
};
exports.PaymentController = PaymentController;
__decorate([
    (0, common_1.Get)('config/wechat'),
    (0, swagger_1.ApiOperation)({
        summary: '获取微信支付配置状态（调试用）',
        description: '检查微信支付相关配置是否正确，用于调试支付问题'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '微信支付配置状态',
        schema: {
            example: {
                appId: 'wx280e45091c5ef854',
                mchId: '1619236596',
                hasApiKey: true,
                hasSerialNo: true,
                hasNotifyUrl: true,
                hasPrivateKey: true,
                notifyUrl: 'https://localhost:3001/api/v1/payment/notify',
                isConfigured: true,
                timestamp: '2025-06-21T12:30:00.000Z'
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getWechatPayConfig", null);
__decorate([
    (0, common_1.Get)('vip-packages'),
    (0, swagger_1.ApiOperation)({ summary: '获取VIP套餐列表' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'VIP套餐列表获取成功',
        type: [payment_dto_1.VipPackageDto],
        schema: {
            example: [
                {
                    id: 'vip_monthly',
                    name: 'VIP月卡',
                    description: '30天VIP特权，无限制解锁关卡',
                    price: 2900,
                    duration: 30,
                    sortOrder: 1,
                    isActive: true,
                    createdAt: '2023-12-01 10:00:00',
                    updatedAt: '2023-12-01 10:00:00'
                }
            ]
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getVipPackages", null);
__decorate([
    (0, common_1.Get)('vip-packages/:id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取VIP套餐详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'VIP套餐ID', example: 'vip_monthly' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'VIP套餐详情获取成功',
        type: payment_dto_1.VipPackageDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP套餐不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getVipPackageById", null);
__decorate([
    (0, common_1.Post)('vip-packages'),
    (0, swagger_1.ApiOperation)({ summary: '创建VIP套餐（ID自动生成）' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'VIP套餐创建成功，ID自动生成',
        type: payment_dto_1.VipPackageDto,
        schema: {
            example: {
                id: 'vip_monthly_30d_a1b2',
                name: 'VIP月卡',
                description: '30天VIP特权，无限制解锁关卡',
                price: 2900,
                duration: 30,
                sortOrder: 1,
                isActive: true,
                createdAt: '2023-12-01 10:00:00',
                updatedAt: '2023-12-01 10:00:00'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_dto_1.CreateVipPackageDto]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "createVipPackage", null);
__decorate([
    (0, common_1.Put)('vip-packages/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新VIP套餐' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'VIP套餐ID', example: 'vip_monthly' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'VIP套餐更新成功',
        type: payment_dto_1.VipPackageDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP套餐不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, payment_dto_1.UpdateVipPackageDto]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "updateVipPackage", null);
__decorate([
    (0, common_1.Delete)('vip-packages/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除VIP套餐' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'VIP套餐ID', example: 'vip_monthly' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'VIP套餐删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP套餐不存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '存在相关订单，无法删除' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "deleteVipPackage", null);
__decorate([
    (0, common_1.Post)('create-order'),
    (0, swagger_1.ApiOperation)({ summary: '创建支付订单' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '支付订单创建成功，返回小程序支付参数',
        type: payment_dto_1.MiniProgramPaymentResponse,
        schema: {
            example: {
                appId: 'wx1234567890abcdef',
                timeStamp: '1640995200',
                nonceStr: 'abc123def456',
                package: 'prepay_id=wx123456789012345678901234567890',
                signType: 'RSA',
                paySign: 'signature_string_here'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数错误或创建订单失败' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在或VIP套餐不存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "createPaymentOrder", null);
__decorate([
    (0, common_1.Post)('notify'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '微信支付回调通知' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '回调处理成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '回调处理失败' }),
    __param(0, (0, common_1.Headers)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, payment_dto_1.PaymentNotifyDto]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "handlePaymentNotify", null);
__decorate([
    (0, common_1.Get)('query/:out_trade_no'),
    (0, swagger_1.ApiOperation)({ summary: '查询支付订单状态' }),
    (0, swagger_1.ApiParam)({ name: 'out_trade_no', description: '商户订单号', example: 'ORDER_1640995200_1234' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '订单查询成功',
        schema: {
            example: {
                id: 'pay-order-12345678-1234',
                out_trade_no: 'ORDER_1640995200_1234',
                transaction_id: 'wx123456789012345678901234567890',
                description: 'VIP月卡',
                total: 2900,
                status: 'SUCCESS',
                vip_package_id: 'vip_monthly',
                paid_at: '2023-12-01 12:00:00',
                created_at: '2023-12-01 11:30:00'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('out_trade_no')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "queryPaymentOrder", null);
__decorate([
    (0, common_1.Post)('refresh/:out_trade_no'),
    (0, swagger_1.ApiOperation)({
        summary: '手动刷新支付订单状态',
        description: '主动查询微信支付状态并同步到本地订单，用于调试支付状态同步问题'
    }),
    (0, swagger_1.ApiParam)({ name: 'out_trade_no', description: '商户订单号', example: 'ORDER_1640995200_1234' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '状态刷新成功',
        schema: {
            example: {
                message: '订单状态已刷新',
                localStatus: 'SUCCESS',
                wechatStatus: 'SUCCESS',
                synced: true
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('out_trade_no')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "refreshPaymentOrderStatus", null);
__decorate([
    (0, common_1.Get)('orders'),
    (0, swagger_1.ApiOperation)({ summary: '获取支付订单列表（管理员）' }),
    (0, swagger_1.ApiQuery)({ name: 'page', description: '页码', example: 1, required: false }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', description: '每页数量', example: 20, required: false }),
    (0, swagger_1.ApiQuery)({ name: 'search', description: '搜索关键词（订单号、用户ID、描述）', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'status', description: '订单状态', enum: ['PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED'], required: false }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', description: '开始日期', example: '2023-12-01', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', description: '结束日期', example: '2023-12-31', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'userId', description: '用户ID（获取特定用户订单）', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '订单列表获取成功',
        schema: {
            example: {
                orders: [
                    {
                        id: 'pay-order-12345678-1234',
                        userId: '12345678',
                        openid: 'wx_openid_123456',
                        out_trade_no: 'ORDER_1640995200_1234',
                        transaction_id: 'wx123456789012345678901234567890',
                        description: 'VIP月卡',
                        total: 2900,
                        status: 'SUCCESS',
                        vip_package_id: 'vip_monthly',
                        paid_at: '2023-12-01 12:00:00',
                        expires_at: '2023-12-01 12:30:00',
                        created_at: '2023-12-01 11:30:00',
                        updated_at: '2023-12-01 12:00:00'
                    }
                ],
                total: 1,
                page: 1,
                pageSize: 20,
                totalPages: 1
            }
        }
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('status')),
    __param(4, (0, common_1.Query)('startDate')),
    __param(5, (0, common_1.Query)('endDate')),
    __param(6, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getPaymentOrders", null);
__decorate([
    (0, common_1.Get)('orders/user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户支付订单列表' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID', example: '12345678' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '订单列表获取成功',
        schema: {
            example: [
                {
                    id: 'pay-order-12345678-1234',
                    out_trade_no: 'ORDER_1640995200_1234',
                    transaction_id: 'wx123456789012345678901234567890',
                    description: 'VIP月卡',
                    total: 2900,
                    status: 'SUCCESS',
                    vip_package_id: 'vip_monthly',
                    paid_at: '2023-12-01 12:00:00',
                    created_at: '2023-12-01 11:30:00'
                }
            ]
        }
    }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getUserPaymentOrders", null);
__decorate([
    (0, common_1.Get)('orders/stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取支付订单统计信息' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', description: '开始日期', example: '2023-12-01', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', description: '结束日期', example: '2023-12-31', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '统计信息获取成功',
        schema: {
            example: {
                total: 100,
                pending: 10,
                success: 80,
                failed: 8,
                cancelled: 2,
                refunded: 0,
                totalAmount: 290000,
                successAmount: 232000,
                pendingAmount: 29000,
                successRate: 80.0,
                avgOrderAmount: 2900
            }
        }
    }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getPaymentOrderStats", null);
__decorate([
    (0, common_1.Get)('orders/:id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取支付订单详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '订单ID', example: 'pay-order-12345678-1234' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '订单详情获取成功',
        schema: {
            example: {
                id: 'pay-order-12345678-1234',
                userId: '12345678',
                openid: 'wx_openid_123456',
                out_trade_no: 'ORDER_1640995200_1234',
                transaction_id: 'wx123456789012345678901234567890',
                description: 'VIP月卡',
                total: 2900,
                status: 'SUCCESS',
                vip_package_id: 'vip_monthly',
                prepay_id: 'wx201410272009395522657a690389285100',
                detail: 'VIP月卡详情',
                attach: '{"userId":"12345678","packageId":"vip_monthly"}',
                paid_at: '2023-12-01 12:00:00',
                expires_at: '2023-12-01 12:30:00',
                created_at: '2023-12-01 11:30:00',
                updated_at: '2023-12-01 12:00:00'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getPaymentOrderById", null);
__decorate([
    (0, common_1.Post)('cancel/:out_trade_no'),
    (0, swagger_1.ApiOperation)({ summary: '取消支付订单' }),
    (0, swagger_1.ApiParam)({ name: 'out_trade_no', description: '商户订单号', example: 'ORDER_1640995200_1234' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '订单取消成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在或无法取消' }),
    __param(0, (0, common_1.Param)('out_trade_no')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "cancelPaymentOrder", null);
exports.PaymentController = PaymentController = PaymentController_1 = __decorate([
    (0, swagger_1.ApiTags)('支付管理'),
    (0, common_1.Controller)('payment'),
    __metadata("design:paramtypes", [payment_service_1.PaymentService,
        wechat_pay_service_1.WechatPayService])
], PaymentController);
//# sourceMappingURL=payment.controller.js.map