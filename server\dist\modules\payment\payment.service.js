"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const payment_order_entity_1 = require("./entities/payment-order.entity");
const wechat_pay_service_1 = require("./services/wechat-pay.service");
const user_service_1 = require("../user/user.service");
const date_formatter_1 = require("../../common/utils/date-formatter");
const id_generator_1 = require("../../common/utils/id-generator");
let PaymentService = PaymentService_1 = class PaymentService {
    paymentOrderModel;
    vipPackageModel;
    wechatPayService;
    userService;
    logger = new common_1.Logger(PaymentService_1.name);
    PAYMENT_ORDER_EXPIRE_MINUTES = 30;
    PAYMENT_ORDER_EXPIRE_MS = this.PAYMENT_ORDER_EXPIRE_MINUTES * 60 * 1000;
    constructor(paymentOrderModel, vipPackageModel, wechatPayService, userService) {
        this.paymentOrderModel = paymentOrderModel;
        this.vipPackageModel = vipPackageModel;
        this.wechatPayService = wechatPayService;
        this.userService = userService;
    }
    generateOrderNo() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `ORDER_${timestamp}_${random}`;
    }
    getPaymentOrderExpireMinutes() {
        return this.PAYMENT_ORDER_EXPIRE_MINUTES;
    }
    async checkAndHandleExistingOrder(userId, packageId, openid) {
        const existingOrder = await this.paymentOrderModel.findOne({
            userId: userId,
            status: payment_order_entity_1.PaymentStatus.PENDING,
            expires_at: { $gt: new Date() }
        }).exec();
        if (!existingOrder) {
            return null;
        }
        this.logger.log(`发现用户${userId}的未完成订单: ${existingOrder.out_trade_no}`);
        try {
            const wechatOrder = await this.wechatPayService.queryOrder(existingOrder.out_trade_no);
            if (wechatOrder) {
                await this.syncWechatOrderStatus(existingOrder, wechatOrder);
                if (existingOrder.status !== payment_order_entity_1.PaymentStatus.PENDING) {
                    return null;
                }
            }
        }
        catch (error) {
            this.logger.warn(`查询微信订单状态失败: ${existingOrder.out_trade_no}`, error.message);
        }
        if (existingOrder.vip_package_id === packageId && existingOrder.prepay_id) {
            this.logger.log(`重新返回相同套餐的支付参数: ${existingOrder.out_trade_no}`);
            try {
                const payParams = this.wechatPayService.generateMiniProgramPayParams(existingOrder.prepay_id);
                return payParams;
            }
            catch (error) {
                this.logger.warn(`重新生成支付参数失败，将创建新订单: ${error.message}`);
                existingOrder.status = payment_order_entity_1.PaymentStatus.FAILED;
                await existingOrder.save();
                return null;
            }
        }
        if (existingOrder.vip_package_id !== packageId) {
            this.logger.log(`取消不同套餐的旧订单: ${existingOrder.out_trade_no}`);
            existingOrder.status = payment_order_entity_1.PaymentStatus.CANCELLED;
            await existingOrder.save();
            return null;
        }
        if (!existingOrder.prepay_id) {
            this.logger.log(`取消没有prepay_id的不完整订单: ${existingOrder.out_trade_no}`);
            existingOrder.status = payment_order_entity_1.PaymentStatus.CANCELLED;
            await existingOrder.save();
            return null;
        }
        return null;
    }
    async syncWechatOrderStatus(paymentOrder, wechatOrder) {
        const oldStatus = paymentOrder.status;
        switch (wechatOrder.trade_state) {
            case 'SUCCESS':
                paymentOrder.status = payment_order_entity_1.PaymentStatus.SUCCESS;
                paymentOrder.transaction_id = wechatOrder.transaction_id;
                paymentOrder.paid_at = new Date(wechatOrder.success_time);
                try {
                    await this.activateUserVip(paymentOrder.userId, paymentOrder.vip_package_id);
                    this.logger.log(`用户VIP激活成功: userId=${paymentOrder.userId}, packageId=${paymentOrder.vip_package_id}`);
                }
                catch (error) {
                    this.logger.error('激活用户VIP失败:', error);
                }
                break;
            case 'REFUND':
                paymentOrder.status = payment_order_entity_1.PaymentStatus.REFUNDED;
                break;
            case 'NOTPAY':
            case 'USERPAYING':
                break;
            case 'CLOSED':
            case 'REVOKED':
            case 'PAYERROR':
                paymentOrder.status = payment_order_entity_1.PaymentStatus.FAILED;
                break;
            default:
                this.logger.warn(`未知的微信支付状态: ${wechatOrder.trade_state}`);
                break;
        }
        if (oldStatus !== paymentOrder.status) {
            await paymentOrder.save();
            this.logger.log(`订单状态已同步: ${paymentOrder.out_trade_no} ${oldStatus} -> ${paymentOrder.status}`);
        }
    }
    async generateUniquePaymentOrderId() {
        let id;
        let attempts = 0;
        const maxAttempts = 10;
        do {
            attempts++;
            const timestamp = Date.now().toString().slice(-8);
            const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            id = `pay-order-${timestamp}-${random}`;
            const existingOrder = await this.paymentOrderModel.findOne({ id }).exec();
            if (!existingOrder) {
                break;
            }
            if (attempts >= maxAttempts) {
                throw new common_1.BadRequestException('无法生成唯一的支付订单ID，请稍后重试');
            }
        } while (true);
        return id;
    }
    async getVipPackages() {
        const packages = await this.vipPackageModel
            .find({ isActive: true })
            .sort({ sortOrder: 1 })
            .exec();
        return packages.map(pkg => ({
            id: pkg.id,
            name: pkg.name,
            description: pkg.description,
            price: pkg.price,
            duration: pkg.duration,
            sortOrder: pkg.sortOrder,
            isActive: pkg.isActive,
            createdAt: (0, date_formatter_1.formatDate)(pkg.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(pkg.updatedAt),
        }));
    }
    async getAllVipPackages() {
        const packages = await this.vipPackageModel
            .find({})
            .sort({ sortOrder: 1 })
            .exec();
        return packages.map(pkg => ({
            id: pkg.id,
            name: pkg.name,
            description: pkg.description,
            price: pkg.price,
            duration: pkg.duration,
            sortOrder: pkg.sortOrder,
            isActive: pkg.isActive,
            createdAt: (0, date_formatter_1.formatDate)(pkg.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(pkg.updatedAt),
        }));
    }
    async createVipPackage(createDto) {
        const packageId = await (0, id_generator_1.generateUniqueId)(async (id) => {
            const existing = await this.vipPackageModel.findOne({ id }).exec();
            return !!existing;
        }, () => (0, id_generator_1.generateSemanticVipPackageId)(createDto.name, createDto.duration), 10);
        let sortOrder = createDto.sortOrder;
        if (!sortOrder) {
            const maxSortOrder = await this.vipPackageModel
                .findOne({})
                .sort({ sortOrder: -1 })
                .exec();
            sortOrder = maxSortOrder ? maxSortOrder.sortOrder + 1 : 1;
        }
        const newPackage = new this.vipPackageModel({
            id: packageId,
            name: createDto.name,
            description: createDto.description,
            price: createDto.price,
            duration: createDto.duration,
            sortOrder,
            isActive: createDto.isActive !== undefined ? createDto.isActive : true,
        });
        const savedPackage = await newPackage.save();
        this.logger.log(`创建VIP套餐成功: ${savedPackage.id} - ${savedPackage.name}`);
        return {
            id: savedPackage.id,
            name: savedPackage.name,
            description: savedPackage.description,
            price: savedPackage.price,
            duration: savedPackage.duration,
            sortOrder: savedPackage.sortOrder,
            isActive: savedPackage.isActive,
            createdAt: (0, date_formatter_1.formatDate)(savedPackage.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(savedPackage.updatedAt),
        };
    }
    async updateVipPackage(id, updateDto) {
        const existingPackage = await this.vipPackageModel.findOne({ id }).exec();
        if (!existingPackage) {
            throw new common_1.NotFoundException(`VIP套餐 "${id}" 不存在`);
        }
        if (updateDto.name !== undefined)
            existingPackage.name = updateDto.name;
        if (updateDto.description !== undefined)
            existingPackage.description = updateDto.description;
        if (updateDto.price !== undefined)
            existingPackage.price = updateDto.price;
        if (updateDto.duration !== undefined)
            existingPackage.duration = updateDto.duration;
        if (updateDto.sortOrder !== undefined)
            existingPackage.sortOrder = updateDto.sortOrder;
        if (updateDto.isActive !== undefined)
            existingPackage.isActive = updateDto.isActive;
        const updatedPackage = await existingPackage.save();
        this.logger.log(`更新VIP套餐成功: ${updatedPackage.id} - ${updatedPackage.name}`);
        return {
            id: updatedPackage.id,
            name: updatedPackage.name,
            description: updatedPackage.description,
            price: updatedPackage.price,
            duration: updatedPackage.duration,
            sortOrder: updatedPackage.sortOrder,
            isActive: updatedPackage.isActive,
            createdAt: (0, date_formatter_1.formatDate)(updatedPackage.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(updatedPackage.updatedAt),
        };
    }
    async deleteVipPackage(id) {
        const existingPackage = await this.vipPackageModel.findOne({ id }).exec();
        if (!existingPackage) {
            throw new common_1.NotFoundException(`VIP套餐 "${id}" 不存在`);
        }
        const relatedOrders = await this.paymentOrderModel.findOne({ vip_package_id: id }).exec();
        if (relatedOrders) {
            throw new common_1.BadRequestException(`无法删除套餐 "${id}"，存在相关的支付订单`);
        }
        await this.vipPackageModel.deleteOne({ id }).exec();
        this.logger.log(`删除VIP套餐成功: ${id}`);
    }
    async getVipPackageById(id) {
        const vipPackage = await this.vipPackageModel.findOne({ id }).exec();
        if (!vipPackage) {
            throw new common_1.NotFoundException(`VIP套餐 "${id}" 不存在`);
        }
        return {
            id: vipPackage.id,
            name: vipPackage.name,
            description: vipPackage.description,
            price: vipPackage.price,
            duration: vipPackage.duration,
            sortOrder: vipPackage.sortOrder,
            isActive: vipPackage.isActive,
            createdAt: (0, date_formatter_1.formatDate)(vipPackage.createdAt),
            updatedAt: (0, date_formatter_1.formatDate)(vipPackage.updatedAt),
        };
    }
    async getVipPackageDocument(packageId) {
        const vipPackage = await this.vipPackageModel.findOne({ id: packageId, isActive: true }).exec();
        if (!vipPackage) {
            throw new common_1.NotFoundException(`VIP套餐 "${packageId}" 不存在或已下架`);
        }
        return vipPackage;
    }
    async createPaymentOrder(openid, packageId) {
        const user = await this.userService.getUserByOpenid(openid);
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const vipPackage = await this.getVipPackageDocument(packageId);
        const existingPayParams = await this.checkAndHandleExistingOrder(user.id, packageId, openid);
        if (existingPayParams) {
            return existingPayParams;
        }
        const out_trade_no = this.generateOrderNo();
        const orderId = await this.generateUniquePaymentOrderId();
        const paymentOrder = new this.paymentOrderModel({
            id: orderId,
            userId: user.id,
            openid: openid,
            out_trade_no: out_trade_no,
            description: vipPackage.name,
            total: vipPackage.price,
            status: payment_order_entity_1.PaymentStatus.PENDING,
            vip_package_id: packageId,
            detail: vipPackage.description,
            attach: JSON.stringify({ userId: user.id, packageId }),
            expires_at: new Date(Date.now() + this.PAYMENT_ORDER_EXPIRE_MS),
        });
        try {
            const wechatResponse = await this.wechatPayService.createOrder({
                description: vipPackage.name,
                out_trade_no: out_trade_no,
                total: vipPackage.price,
                openid: openid,
                detail: vipPackage.description,
                attach: JSON.stringify({ userId: user.id, packageId }),
                expireMinutes: this.PAYMENT_ORDER_EXPIRE_MINUTES,
            });
            paymentOrder.prepay_id = wechatResponse.prepay_id;
            await paymentOrder.save();
            const payParams = this.wechatPayService.generateMiniProgramPayParams(wechatResponse.prepay_id);
            this.logger.log(`创建支付订单成功: userId=${user.id}, orderId=${orderId}, amount=${vipPackage.price}`);
            return payParams;
        }
        catch (error) {
            this.logger.error('创建支付订单失败:', error);
            throw new common_1.BadRequestException('创建支付订单失败，请稍后重试');
        }
    }
    async handlePaymentNotify(notifyData) {
        const { out_trade_no, transaction_id, trade_state, success_time, payer, amount } = notifyData;
        this.logger.log('处理支付回调开始', {
            out_trade_no,
            transaction_id,
            trade_state,
            openid: payer?.openid
        });
        try {
            const paymentOrder = await this.paymentOrderModel.findOne({ out_trade_no }).exec();
            if (!paymentOrder) {
                this.logger.error(`支付回调：订单不存在 ${out_trade_no}`);
                throw new Error(`订单不存在: ${out_trade_no}`);
            }
            if (amount && amount.total !== paymentOrder.total) {
                this.logger.error(`支付回调：订单金额不匹配`, {
                    out_trade_no,
                    expected: paymentOrder.total,
                    actual: amount.total
                });
                throw new Error(`订单金额不匹配: ${out_trade_no}`);
            }
            if (payer?.openid && payer.openid !== paymentOrder.openid) {
                this.logger.error(`支付回调：用户openid不匹配`, {
                    out_trade_no,
                    expected: paymentOrder.openid,
                    actual: payer.openid
                });
                throw new Error(`用户openid不匹配: ${out_trade_no}`);
            }
            if (paymentOrder.status === payment_order_entity_1.PaymentStatus.SUCCESS) {
                this.logger.warn(`支付回调：订单已处理 ${out_trade_no}, 当前状态: ${paymentOrder.status}`);
                return;
            }
            if (paymentOrder.status !== payment_order_entity_1.PaymentStatus.PENDING) {
                this.logger.warn(`支付回调：订单状态异常 ${out_trade_no}, 当前状态: ${paymentOrder.status}`);
                return;
            }
            switch (trade_state) {
                case 'SUCCESS':
                    paymentOrder.status = payment_order_entity_1.PaymentStatus.SUCCESS;
                    paymentOrder.transaction_id = transaction_id;
                    paymentOrder.paid_at = new Date(success_time);
                    await paymentOrder.save();
                    await this.activateUserVip(paymentOrder.userId, paymentOrder.vip_package_id);
                    this.logger.log(`支付成功处理完成`, {
                        userId: paymentOrder.userId,
                        orderId: paymentOrder.id,
                        amount: paymentOrder.total,
                        transaction_id
                    });
                    break;
                case 'CLOSED':
                case 'PAYERROR':
                case 'REVOKED':
                    paymentOrder.status = payment_order_entity_1.PaymentStatus.FAILED;
                    await paymentOrder.save();
                    this.logger.log(`支付失败处理完成`, {
                        userId: paymentOrder.userId,
                        orderId: paymentOrder.id,
                        trade_state
                    });
                    break;
                default:
                    this.logger.warn(`未处理的支付状态: ${trade_state}`, { out_trade_no });
            }
        }
        catch (error) {
            this.logger.error('处理支付回调失败:', {
                out_trade_no,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }
    async activateUserVip(userId, packageId) {
        try {
            const vipPackage = await this.getVipPackageDocument(packageId);
            const now = new Date();
            const vipExpiresAt = new Date(now.getTime() + vipPackage.duration * 24 * 60 * 60 * 1000);
            await this.userService.update(userId, {
                isVip: true,
                vipExpiresAt,
                dailyUnlockLimit: 999999,
            });
            this.logger.log(`用户VIP激活成功: userId=${userId}, package=${packageId}, duration=${vipPackage.duration}天, 过期时间=${vipExpiresAt.toISOString()}`);
        }
        catch (error) {
            this.logger.error('激活用户VIP失败:', error);
            throw error;
        }
    }
    async queryPaymentOrder(out_trade_no) {
        const paymentOrder = await this.paymentOrderModel.findOne({ out_trade_no }).exec();
        if (!paymentOrder) {
            throw new common_1.NotFoundException('支付订单不存在');
        }
        if (paymentOrder.status === payment_order_entity_1.PaymentStatus.PENDING && paymentOrder.expires_at > new Date()) {
            try {
                const wechatOrder = await this.wechatPayService.queryOrder(out_trade_no);
                if (wechatOrder) {
                    switch (wechatOrder.trade_state) {
                        case 'SUCCESS':
                            paymentOrder.status = payment_order_entity_1.PaymentStatus.SUCCESS;
                            paymentOrder.transaction_id = wechatOrder.transaction_id;
                            paymentOrder.paid_at = new Date(wechatOrder.success_time);
                            await paymentOrder.save();
                            await this.activateUserVip(paymentOrder.userId, paymentOrder.vip_package_id);
                            this.logger.log(`订单状态同步成功: ${out_trade_no} -> SUCCESS`);
                            break;
                        case 'CLOSED':
                        case 'PAYERROR':
                        case 'REVOKED':
                            paymentOrder.status = payment_order_entity_1.PaymentStatus.FAILED;
                            await paymentOrder.save();
                            this.logger.log(`订单状态同步成功: ${out_trade_no} -> FAILED`);
                            break;
                        case 'USERPAYING':
                        case 'NOTPAY':
                            this.logger.debug(`订单仍在支付中: ${out_trade_no} -> ${wechatOrder.trade_state}`);
                            break;
                        default:
                            this.logger.warn(`未知的微信支付状态: ${wechatOrder.trade_state}`);
                    }
                }
            }
            catch (error) {
                this.logger.error('查询微信支付订单失败:', error);
            }
        }
        else if (paymentOrder.status === payment_order_entity_1.PaymentStatus.PENDING && paymentOrder.expires_at <= new Date()) {
            paymentOrder.status = payment_order_entity_1.PaymentStatus.FAILED;
            await paymentOrder.save();
            this.logger.log(`订单已过期，更新状态: ${out_trade_no} -> FAILED`);
        }
        return {
            id: paymentOrder.id,
            out_trade_no: paymentOrder.out_trade_no,
            transaction_id: paymentOrder.transaction_id,
            description: paymentOrder.description,
            total: paymentOrder.total,
            status: paymentOrder.status,
            vip_package_id: paymentOrder.vip_package_id,
            paid_at: paymentOrder.paid_at ? (0, date_formatter_1.formatDate)(paymentOrder.paid_at) : null,
            expires_at: (0, date_formatter_1.formatDate)(paymentOrder.expires_at),
            created_at: (0, date_formatter_1.formatDate)(paymentOrder.createdAt),
        };
    }
    async getPaymentOrdersList(params) {
        const { page, pageSize, search, status, startDate, endDate, userId } = params;
        const query = {};
        if (userId) {
            query.userId = userId;
        }
        if (status) {
            query.status = status;
        }
        if (startDate || endDate) {
            query.createdAt = {};
            if (startDate) {
                query.createdAt.$gte = new Date(startDate);
            }
            if (endDate) {
                query.createdAt.$lte = new Date(endDate + ' 23:59:59');
            }
        }
        if (search) {
            query.$or = [
                { out_trade_no: { $regex: search, $options: 'i' } },
                { transaction_id: { $regex: search, $options: 'i' } },
                { userId: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { openid: { $regex: search, $options: 'i' } },
            ];
        }
        const skip = (page - 1) * pageSize;
        const [orders, total] = await Promise.all([
            this.paymentOrderModel
                .find(query)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(pageSize)
                .exec(),
            this.paymentOrderModel.countDocuments(query).exec(),
        ]);
        const formattedOrders = orders.map(order => ({
            id: order.id,
            userId: order.userId,
            openid: order.openid,
            out_trade_no: order.out_trade_no,
            transaction_id: order.transaction_id,
            description: order.description,
            total: order.total,
            status: order.status,
            vip_package_id: order.vip_package_id,
            paid_at: order.paid_at ? (0, date_formatter_1.formatDate)(order.paid_at) : null,
            expires_at: (0, date_formatter_1.formatDate)(order.expires_at),
            created_at: (0, date_formatter_1.formatDate)(order.createdAt),
            updated_at: (0, date_formatter_1.formatDate)(order.updatedAt),
        }));
        return {
            orders: formattedOrders,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
    async getUserPaymentOrders(userId) {
        const orders = await this.paymentOrderModel
            .find({ userId })
            .sort({ createdAt: -1 })
            .exec();
        return orders.map(order => ({
            id: order.id,
            out_trade_no: order.out_trade_no,
            transaction_id: order.transaction_id,
            description: order.description,
            total: order.total,
            status: order.status,
            vip_package_id: order.vip_package_id,
            paid_at: order.paid_at ? (0, date_formatter_1.formatDate)(order.paid_at) : null,
            created_at: (0, date_formatter_1.formatDate)(order.createdAt),
        }));
    }
    async getPaymentOrderStats(params) {
        const { startDate, endDate } = params;
        const query = {};
        if (startDate || endDate) {
            query.createdAt = {};
            if (startDate) {
                query.createdAt.$gte = new Date(startDate);
            }
            if (endDate) {
                query.createdAt.$lte = new Date(endDate + ' 23:59:59');
            }
        }
        const stats = await this.paymentOrderModel.aggregate([
            { $match: query },
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    pending: { $sum: { $cond: [{ $eq: ['$status', 'PENDING'] }, 1, 0] } },
                    success: { $sum: { $cond: [{ $eq: ['$status', 'SUCCESS'] }, 1, 0] } },
                    failed: { $sum: { $cond: [{ $eq: ['$status', 'FAILED'] }, 1, 0] } },
                    cancelled: { $sum: { $cond: [{ $eq: ['$status', 'CANCELLED'] }, 1, 0] } },
                    refunded: { $sum: { $cond: [{ $eq: ['$status', 'REFUNDED'] }, 1, 0] } },
                    totalAmount: { $sum: '$total' },
                    successAmount: {
                        $sum: {
                            $cond: [{ $eq: ['$status', 'SUCCESS'] }, '$total', 0]
                        }
                    },
                    pendingAmount: {
                        $sum: {
                            $cond: [{ $eq: ['$status', 'PENDING'] }, '$total', 0]
                        }
                    },
                }
            }
        ]).exec();
        const result = stats[0] || {
            total: 0,
            pending: 0,
            success: 0,
            failed: 0,
            cancelled: 0,
            refunded: 0,
            totalAmount: 0,
            successAmount: 0,
            pendingAmount: 0,
        };
        result.successRate = result.total > 0 ? (result.success / result.total * 100) : 0;
        result.avgOrderAmount = result.total > 0 ? (result.totalAmount / result.total) : 0;
        result.successRate = Math.round(result.successRate * 100) / 100;
        result.avgOrderAmount = Math.round(result.avgOrderAmount * 100) / 100;
        return result;
    }
    async getPaymentOrderById(id) {
        const order = await this.paymentOrderModel.findOne({ id }).exec();
        if (!order) {
            throw new common_1.NotFoundException('支付订单不存在');
        }
        return {
            id: order.id,
            userId: order.userId,
            openid: order.openid,
            out_trade_no: order.out_trade_no,
            transaction_id: order.transaction_id,
            description: order.description,
            total: order.total,
            status: order.status,
            vip_package_id: order.vip_package_id,
            prepay_id: order.prepay_id,
            detail: order.detail,
            attach: order.attach,
            paid_at: order.paid_at ? (0, date_formatter_1.formatDate)(order.paid_at) : null,
            expires_at: (0, date_formatter_1.formatDate)(order.expires_at),
            created_at: (0, date_formatter_1.formatDate)(order.createdAt),
            updated_at: (0, date_formatter_1.formatDate)(order.updatedAt),
        };
    }
    async refreshPaymentOrderStatus(out_trade_no) {
        const paymentOrder = await this.paymentOrderModel.findOne({ out_trade_no }).exec();
        if (!paymentOrder) {
            throw new common_1.NotFoundException('支付订单不存在');
        }
        const oldStatus = paymentOrder.status;
        let wechatStatus = null;
        let synced = false;
        try {
            const wechatOrder = await this.wechatPayService.queryOrder(out_trade_no);
            if (wechatOrder) {
                wechatStatus = wechatOrder.trade_state;
                await this.syncWechatOrderStatus(paymentOrder, wechatOrder);
                synced = oldStatus !== paymentOrder.status;
                this.logger.log(`手动刷新订单状态: ${out_trade_no} ${oldStatus} -> ${paymentOrder.status}`);
            }
            else {
                this.logger.warn(`微信订单不存在: ${out_trade_no}`);
            }
        }
        catch (error) {
            this.logger.error(`刷新订单状态失败: ${out_trade_no}`, error.message);
            throw new common_1.BadRequestException(`刷新订单状态失败: ${error.message}`);
        }
        return {
            message: synced ? '订单状态已刷新' : '订单状态无变化',
            out_trade_no,
            localStatus: paymentOrder.status,
            wechatStatus,
            synced,
            oldStatus,
            transaction_id: paymentOrder.transaction_id,
            paid_at: paymentOrder.paid_at ? (0, date_formatter_1.formatDate)(paymentOrder.paid_at) : null,
        };
    }
    async cancelPaymentOrder(out_trade_no, userId) {
        const paymentOrder = await this.paymentOrderModel.findOne({
            out_trade_no,
            userId,
            status: payment_order_entity_1.PaymentStatus.PENDING
        }).exec();
        if (!paymentOrder) {
            throw new common_1.NotFoundException('支付订单不存在或无法取消');
        }
        paymentOrder.status = payment_order_entity_1.PaymentStatus.CANCELLED;
        await paymentOrder.save();
        this.logger.log(`取消支付订单: userId=${userId}, orderId=${paymentOrder.id}`);
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = PaymentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(payment_order_entity_1.PaymentOrder.name)),
    __param(1, (0, mongoose_1.InjectModel)(payment_order_entity_1.VipPackage.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        wechat_pay_service_1.WechatPayService,
        user_service_1.UserService])
], PaymentService);
//# sourceMappingURL=payment.service.js.map