import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, Matches } from 'class-validator';

export class WeixinLoginDto {
  @ApiProperty({ description: '微信小程序登录凭证code', example: '081Kq4Ga1MSox41ufaGa1elzqd4Kq4Gn' })
  @IsString()
  @IsNotEmpty({ message: 'code不能为空' })
  code: string;

  @ApiProperty({ description: '用户手机号（可选）', example: '13800138000', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '手机号不能为空字符串' })
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的手机号码' })
  phone?: string;

  @ApiProperty({ description: '用户昵称（可选）', example: '微信用户', required: false })
  @IsOptional()
  @IsString()
  nickname?: string;

  @ApiProperty({ description: '用户头像URL（可选）', example: 'https://thirdwx.qlogo.cn/mmopen/xxx', required: false })
  @IsOptional()
  @IsString()
  avatarUrl?: string;
}

export class WeixinLoginResponseDto {
  @ApiProperty({ description: '登录状态', example: 'success' })
  status: 'success' | 'need_bind';

  @ApiProperty({ description: '响应消息', example: '登录成功' })
  message: string;

  @ApiProperty({ description: '用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' })
  openid: string;

  @ApiProperty({ description: '会话密钥（仅用于服务端，不返回给客户端）', example: 'session_key_xxx' })
  sessionKey?: string;

  @ApiProperty({ description: '用户信息（登录成功时返回）', required: false })
  userInfo?: {
    id: string;
    maskedPhone?: string;
    nickname?: string;
    avatarUrl?: string;
    unlockedLevels: number;
    completedLevelIds: string[];
    totalGames: number;
    totalCompletions: number;
    lastPlayTime: string;
    createdAt: string;
  };

  @ApiProperty({ description: 'unionid（如果小程序绑定了开放平台）', required: false })
  unionid?: string;
}

export class WeixinPhoneBindDto {
  @ApiProperty({ description: '微信用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' })
  @IsString()
  @IsNotEmpty({ message: 'openid不能为空' })
  openid: string;

  @ApiProperty({ description: '用户手机号', example: '13800138000' })
  @IsString()
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string;

  @ApiProperty({ description: '用户昵称（可选）', example: '微信用户', required: false })
  @IsOptional()
  @IsString()
  nickname?: string;

  @ApiProperty({ description: '用户头像URL（可选）', example: 'https://thirdwx.qlogo.cn/mmopen/xxx', required: false })
  @IsOptional()
  @IsString()
  avatarUrl?: string;
}

export class WeixinSessionCheckDto {
  @ApiProperty({ description: '微信用户openid', example: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE' })
  @IsString()
  @IsNotEmpty({ message: 'openid不能为空' })
  openid: string;

  @ApiProperty({ description: '会话密钥', example: 'session_key_xxx' })
  @IsString()
  @IsNotEmpty({ message: 'sessionKey不能为空' })
  sessionKey: string;
}
