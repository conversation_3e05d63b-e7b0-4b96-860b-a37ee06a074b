# 任务记录

## MongoDB重复键错误修复 (2025-07-07)

### 问题描述
遇到MongoDB错误：`E11000 duplicate key error collection: xxl_dev_db.users index: phone_1 dup key: { phone: "" }`

### 问题分析
1. **根本原因**：用户表的`phone`字段设置了唯一索引(`unique: true`)和稀疏索引(`sparse: true`)
2. **触发条件**：多个用户的`phone`字段都被设置为空字符串`""`
3. **技术细节**：
   - 稀疏索引只忽略`null`和`undefined`值
   - 空字符串`""`仍然被视为有效值并被索引
   - 当多个记录都有相同的空字符串时，触发重复键错误

### 解决方案

#### 1. 修改User实体 (user.entity.ts)
- 在`phone`字段的`@Prop`装饰器中添加`set`函数
- 将空字符串`""`自动转换为`undefined`

```typescript
@Prop({ 
  required: false, 
  unique: true, 
  sparse: true,
  set: (value: string) => value === '' ? undefined : value
})
phone?: string;
```

#### 2. 增强DTO验证
- 在`CreateUserDto`和`WeixinLoginDto`中添加`@IsNotEmpty()`验证
- 确保空字符串在验证层就被拦截

#### 3. 服务层保护
- 在`UserService`中添加`cleanEmptyStrings()`辅助方法
- 在所有创建和更新用户的方法中使用此方法清理空字符串

#### 4. 数据库清理
- 创建清理脚本`fix-empty-phone-strings.js`
- 将现有的空字符串`phone`字段更新为`null`

### 修改的文件
1. `server/src/modules/user/entities/user.entity.ts` - 添加set函数
2. `server/src/modules/user/dto/create-user.dto.ts` - 增强验证
3. `server/src/modules/weixin/dto/weixin-login.dto.ts` - 增强验证
4. `server/src/modules/user/user.service.ts` - 添加清理方法和应用到所有用户创建/更新操作
5. `server/scripts/fix-empty-phone-strings.js` - 数据库清理脚本

### 预防措施
1. **实体层**：自动转换空字符串为undefined
2. **验证层**：拒绝空字符串输入
3. **服务层**：双重保护，确保数据清洁
4. **数据库层**：稀疏索引正确处理null/undefined值

### 执行步骤
1. ✅ 修改代码文件
2. ⏳ 运行数据库清理脚本
3. ⏳ 重启应用
4. ⏳ 验证修复效果

### 注意事项
- 此修复向后兼容，不会影响现有功能
- 空字符串会被自动转换为undefined，符合业务逻辑
- 稀疏索引确保多个undefined值不会冲突
