import { Document } from 'mongoose';
export type UserDocument = User & Document;
export declare class User {
    id: string;
    phone?: string;
    openid?: string;
    nickname?: string;
    avatarUrl?: string;
    unlockedLevels: number;
    completedLevelIds: string[];
    totalGames: number;
    totalCompletions: number;
    lastPlayTime: Date;
    isVip: boolean;
    vipExpiresAt?: Date;
    dailyUnlockLimit: number;
    dailyUnlockCount: number;
    dailyShared: boolean;
    lastPlayDate: string;
    totalShares: number;
    createdAt: Date;
    updatedAt: Date;
}
export declare const UserSchema: import("mongoose").Schema<User, import("mongoose").Model<User, any, any, any, Document<unknown, any, User, any> & User & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, User, Document<unknown, {}, import("mongoose").FlatRecord<User>, {}> & import("mongoose").FlatRecord<User> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
export interface UserEntity {
    id: string;
    phone?: string;
    openid?: string;
    nickname?: string;
    avatarUrl?: string;
    unlockedLevels: number;
    completedLevelIds: string[];
    totalGames: number;
    totalCompletions: number;
    lastPlayTime: Date;
    isVip: boolean;
    vipExpiresAt?: Date;
    dailyUnlockLimit: number;
    dailyUnlockCount: number;
    dailyShared: boolean;
    lastPlayDate: string;
    totalShares: number;
    createdAt: Date;
    updatedAt: Date;
}
