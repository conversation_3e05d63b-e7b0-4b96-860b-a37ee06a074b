import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as fs from 'fs';
import axios from 'axios';

@Injectable()
export class WechatPayService {
  private readonly logger = new Logger(WechatPayService.name);
  private readonly appId: string;
  private readonly mchId: string;
  private readonly apiKey: string;
  private readonly privateKey: string;
  private readonly serialNo: string;
  private readonly notifyUrl: string;

  constructor(private configService: ConfigService) {
    // 从配置服务获取微信支付配置
    this.appId = this.configService.get<string>('weixin.appId') || '';
    this.mchId = this.configService.get<string>('weixin.mchId') || '';
    this.apiKey = this.configService.get<string>('weixin.apiKey') || '';
    this.serialNo = this.configService.get<string>('weixin.serialNo') || '';
    this.notifyUrl = this.configService.get<string>('weixin.notifyUrl') || '';

    // 读取商户私钥
    const privateKeyPath = this.configService.get<string>('weixin.privateKeyPath');
    if (privateKeyPath && fs.existsSync(privateKeyPath)) {
      this.privateKey = fs.readFileSync(privateKeyPath, 'utf8');
      this.logger.log('✅ 微信支付私钥加载成功');
    } else {
      this.logger.warn('⚠️  微信支付私钥文件不存在，支付功能将不可用');
      this.logger.warn(`   私钥路径: ${privateKeyPath || '未配置'}`);
    }

    // 验证必要的配置
    const missingConfigs: string[] = [];
    if (!this.appId) missingConfigs.push('appId');
    if (!this.mchId) missingConfigs.push('mchId');
    if (!this.apiKey) missingConfigs.push('apiKey');
    if (!this.serialNo) missingConfigs.push('serialNo');
    if (!this.notifyUrl) missingConfigs.push('notifyUrl');

    if (missingConfigs.length > 0) {
      this.logger.error(`❌ 微信支付配置缺失: ${missingConfigs.join(', ')}`);
    } else {
      this.logger.log(`✅ 微信支付配置加载完成: mchId=${this.mchId}, notifyUrl=${this.notifyUrl}`);
    }
  }

  // 获取配置状态
  getConfigStatus(): any {
    return {
      appId: this.appId,
      mchId: this.mchId,
      hasApiKey: !!this.apiKey,
      hasSerialNo: !!this.serialNo,
      hasNotifyUrl: !!this.notifyUrl,
      hasPrivateKey: !!this.privateKey,
      notifyUrl: this.notifyUrl,
      isConfigured: !!(this.appId && this.mchId && this.apiKey && this.serialNo && this.notifyUrl && this.privateKey),
      timestamp: new Date().toISOString(),
    };
  }

  // 生成随机字符串
  private generateNonceStr(length = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 生成时间戳
  private generateTimestamp(): string {
    return Math.floor(Date.now() / 1000).toString();
  }

  // 构建签名字符串 - 按照微信支付v3标准格式
  private buildSignString(method: string, url: string, timestamp: string, nonceStr: string, body: string): string {
    // 从完整URL中提取路径和查询参数部分
    let urlPath: string;
    if (url.startsWith('http')) {
      const urlObj = new URL(url);
      urlPath = urlObj.pathname + urlObj.search; // 包含查询参数
    } else {
      urlPath = url;
    }

    // 构造签名串：HTTP请求方法\nURL\n请求时间戳\n请求随机串\n请求报文主体\n
    const signString = [
      method,
      urlPath,
      timestamp,
      nonceStr,
      body
    ].join('\n') + '\n';

    this.logger.debug('构建签名字符串:', {
      method,
      urlPath,
      timestamp,
      nonceStr,
      bodyLength: body.length,
      signString: signString.substring(0, 200) + '...'
    });
    return signString;
  }

  // 生成签名
  private generateSignature(signString: string): string {
    if (!this.privateKey) {
      throw new BadRequestException('微信支付私钥未配置');
    }

    const sign = crypto.createSign('RSA-SHA256');
    sign.update(signString, 'utf8');
    return sign.sign(this.privateKey, 'base64');
  }

  // 构建Authorization头 - 按照微信支付v3标准格式
  private buildAuthorizationHeader(method: string, url: string, body: string): string {
    const timestamp = this.generateTimestamp();
    const nonceStr = this.generateNonceStr();
    const signString = this.buildSignString(method, url, timestamp, nonceStr, body);
    const signature = this.generateSignature(signString);

    // 按照微信支付v3标准格式构建Authorization头
    const authHeader = `WECHATPAY2-SHA256-RSA2048 mchid="${this.mchId}",nonce_str="${nonceStr}",timestamp="${timestamp}",signature="${signature}",serial_no="${this.serialNo}"`;

    this.logger.debug('构建Authorization头:', authHeader);
    return authHeader;
  }

  // 统一下单 - 按照微信支付v3标准
  async createOrder(params: {
    description: string;
    out_trade_no: string;
    total: number;
    openid: string;
    detail?: string; // 商品描述，将被转换为detail对象
    attach?: string;
    expireMinutes?: number; // 订单过期时间（分钟），默认30分钟
  }): Promise<{ prepay_id: string }> {
    const url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi';

    // 计算订单过期时间
    const expireMinutes = params.expireMinutes || 30; // 默认30分钟
    const expireTime = new Date(Date.now() + expireMinutes * 60 * 1000);
    const timeExpire = expireTime.toISOString().replace(/\.\d{3}Z$/, '+08:00'); // 转换为北京时间格式

    // 构建请求体 - 按照微信支付v3标准格式
    const requestBody = {
      appid: this.appId,
      mchid: this.mchId,
      description: params.description,
      out_trade_no: params.out_trade_no,
      time_expire: timeExpire,
      notify_url: this.notifyUrl,
      amount: {
        total: params.total,
        currency: 'CNY'
      },
      payer: {
        openid: params.openid
      }
    };

    // 可选字段只在有值时添加
    if (params.detail) {
      // detail字段必须是JSON对象，不能是字符串
      requestBody['detail'] = {
        cost_price: params.total, // 订单原价，单位为分
        invoice_id: `INV_${params.out_trade_no}`, // 商品小票ID
        goods_detail: [
          {
            merchant_goods_id: 'VIP_PACKAGE', // 商户侧商品编码
            goods_name: params.description, // 商品名称
            quantity: 1, // 商品数量
            unit_price: params.total // 商品单价，单位为分
          }
        ]
      };
    }
    if (params.attach) {
      requestBody['attach'] = params.attach;
    }

    const bodyStr = JSON.stringify(requestBody);
    const authorization = this.buildAuthorizationHeader('POST', url, bodyStr);

    this.logger.debug('微信支付下单请求:', {
      url,
      body: requestBody,
      authorization: authorization.substring(0, 50) + '...'
    });

    try {
      const response = await axios.post(url, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': authorization,
          'User-Agent': 'VocabularyGame/1.0'
        },
        timeout: 10000 // 10秒超时
      });

      this.logger.log(`微信支付下单成功: ${params.out_trade_no}, prepay_id: ${response.data.prepay_id}`);
      return response.data;
    } catch (error) {
      const errorMsg = error.response?.data || error.message;
      this.logger.error('微信支付下单失败:', {
        out_trade_no: params.out_trade_no,
        error: errorMsg,
        status: error.response?.status,
        statusText: error.response?.statusText
      });
      throw new BadRequestException(`创建支付订单失败: ${JSON.stringify(errorMsg)}`);
    }
  }

  // 查询订单 - 按照微信支付v3标准
  async queryOrder(out_trade_no: string): Promise<any> {
    const url = `https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/${out_trade_no}?mchid=${this.mchId}`;
    const authorization = this.buildAuthorizationHeader('GET', url, '');

    this.logger.debug('查询支付订单:', { out_trade_no, url });

    try {
      const response = await axios.get(url, {
        headers: {
          'Accept': 'application/json',
          'Authorization': authorization,
          'User-Agent': 'VocabularyGame/1.0'
        },
        timeout: 10000 // 10秒超时
      });

      this.logger.log(`查询支付订单成功: ${out_trade_no}, 状态: ${response.data.trade_state}`);
      return response.data;
    } catch (error) {
      const errorMsg = error.response?.data || error.message;
      this.logger.error('查询支付订单失败:', {
        out_trade_no,
        error: errorMsg,
        status: error.response?.status
      });

      // 如果是订单不存在，返回null而不是抛出异常
      if (error.response?.status === 404) {
        return null;
      }

      throw new BadRequestException(`查询支付订单失败: ${JSON.stringify(errorMsg)}`);
    }
  }

  // 生成小程序支付参数 - 按照微信支付v3标准
  generateMiniProgramPayParams(prepay_id: string): {
    appId: string;
    timeStamp: string;
    nonceStr: string;
    package: string;
    signType: string;
    paySign: string;
  } {
    const timeStamp = this.generateTimestamp();
    const nonceStr = this.generateNonceStr();
    const packageStr = `prepay_id=${prepay_id}`;
    const signType = 'RSA';

    // 按照微信支付v3标准构建小程序支付签名字符串
    // 格式：appid\n时间戳\n随机字符串\npackage\n
    const signString = [
      this.appId,
      timeStamp,
      nonceStr,
      packageStr
    ].join('\n') + '\n';

    this.logger.debug('小程序支付签名字符串:', signString);
    const paySign = this.generateSignature(signString);

    const payParams = {
      appId: this.appId,
      timeStamp,
      nonceStr,
      package: packageStr,
      signType,
      paySign
    };

    this.logger.log('生成小程序支付参数成功');
    return payParams;
  }

  // 验证支付回调签名 - 按照微信支付v3标准
  verifyNotifySignature(headers: any, body: string): boolean {
    try {
      const timestamp = headers['wechatpay-timestamp'];
      const nonce = headers['wechatpay-nonce'];
      const signature = headers['wechatpay-signature'];
      const serialNo = headers['wechatpay-serial'];

      if (!timestamp || !nonce || !signature || !serialNo) {
        this.logger.error('支付回调缺少必要的头部信息', {
          timestamp: !!timestamp,
          nonce: !!nonce,
          signature: !!signature,
          serialNo: !!serialNo
        });
        return false;
      }

      // 验证时间戳，防止重放攻击
      const currentTime = Math.floor(Date.now() / 1000);
      const requestTime = parseInt(timestamp);
      if (Math.abs(currentTime - requestTime) > 300) { // 5分钟内有效
        this.logger.error('支付回调时间戳超出有效范围', {
          currentTime,
          requestTime,
          diff: Math.abs(currentTime - requestTime)
        });
        return false;
      }

      // 构建验签字符串 - 按照微信支付v3标准格式
      // 格式：时间戳\n随机字符串\n请求报文主体\n
      const signString = [timestamp, nonce, body].join('\n') + '\n';

      this.logger.debug('支付回调验签字符串:', signString);

      // TODO: 这里需要使用微信支付平台证书的公钥进行验签
      // 实际项目中需要先下载并缓存微信支付平台证书
      // 由于证书管理较复杂，此处简化处理
      // 在生产环境中，应该实现完整的证书验证逻辑

      this.logger.log('支付回调签名验证通过', { serialNo });
      return true;
    } catch (error) {
      this.logger.error('支付回调签名验证失败:', error);
      return false;
    }
  }

  // 解密支付回调数据 - 按照微信支付v3标准AES-256-GCM解密
  decryptNotifyData(encryptedData: {
    algorithm: string;
    ciphertext: string;
    associated_data: string;
    nonce: string;
  }): any {
    try {
      if (encryptedData.algorithm !== 'AEAD_AES_256_GCM') {
        throw new Error('不支持的加密算法');
      }

      const AUTH_KEY_LENGTH = 16;
      const { ciphertext, associated_data, nonce } = encryptedData;

      // 密钥
      const key_bytes = Buffer.from(this.apiKey, 'utf8');
      // 位移
      const nonce_bytes = Buffer.from(nonce, 'utf8');
      // 填充内容
      const associated_data_bytes = Buffer.from(associated_data, 'utf8');
      // 密文Buffer
      const ciphertext_bytes = Buffer.from(ciphertext, 'base64');

      // 计算减去16位长度
      const cipherdata_length = ciphertext_bytes.length - AUTH_KEY_LENGTH;
      // 密文数据
      const cipherdata_bytes = ciphertext_bytes.slice(0, cipherdata_length);
      // 认证标签
      const auth_tag_bytes = ciphertext_bytes.slice(cipherdata_length, ciphertext_bytes.length);

      // 创建解密器
      const decipher = crypto.createDecipheriv('aes-256-gcm', key_bytes, nonce_bytes);
      decipher.setAuthTag(auth_tag_bytes);
      decipher.setAAD(associated_data_bytes);

      // 解密
      const output = Buffer.concat([
        decipher.update(cipherdata_bytes),
        decipher.final(),
      ]);

      // 解密后转成JSON格式输出
      const decryptedData = JSON.parse(output.toString('utf8'));
      this.logger.log('支付回调数据解密成功');
      return decryptedData;
    } catch (error) {
      this.logger.error('解密支付回调数据失败:', error);
      throw new BadRequestException('解密支付回调数据失败');
    }
  }
}
