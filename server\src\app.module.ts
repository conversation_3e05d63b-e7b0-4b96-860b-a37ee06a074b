// server/src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { PhraseModule } from './modules/phrase/phrase.module';
import { LevelModule } from './modules/level/level.module';
import { CommonModule } from './common/common.module';
import { ThesaurusModule } from './modules/thesaurus/thesaurus.module';
import { AuthModule } from './modules/auth/auth.module';
import { UserModule } from './modules/user/user.module';
import { ShareModule } from './modules/share/share.module';
import { PaymentModule } from './modules/payment/payment.module';
import { WeixinModule } from './modules/weixin/weixin.module';
import { SettingsModule } from './modules/settings/settings.module';
import { AppService } from './app.service';
import configuration, { configValidationSchema } from './config/configuration';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      validationSchema: configValidationSchema,
      envFilePath: [
        `.env.${process.env.NODE_ENV || 'development'}`,
        '.env',
      ],
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('database.uri'),
        dbName: configService.get<string>('database.name'),
        connectionFactory: (connection) => {
          console.log(`✅ MongoDB 连接成功: ${configService.get<string>('database.name')}`);
          return connection;
        },
      }),
      inject: [ConfigService],
    }),
    CommonModule, // 包含通用工具，如日期格式化
    LevelModule,
    PhraseModule,
    ThesaurusModule,
    AuthModule,
    UserModule,
    ShareModule,
    PaymentModule,
    WeixinModule,
    SettingsModule,
  ],
  controllers: [],
  providers: [AppService],
})
export class AppModule {}
