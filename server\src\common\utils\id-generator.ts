/**
 * ID生成工具
 * 
 * 提供各种类型的ID生成方法
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * 生成VIP套餐ID
 * 格式: vip_yyyymmdd_hhmmss_随机4位
 * 例如: vip_20231201_143022_a1b2
 */
export function generateVipPackageId(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  
  // 生成4位随机字符串
  const randomStr = Math.random().toString(36).substring(2, 6);
  
  return `vip_${year}${month}${day}_${hour}${minute}${second}_${randomStr}`;
}

/**
 * 生成简短的VIP套餐ID
 * 格式: vip_随机8位字符
 * 例如: vip_a1b2c3d4
 */
export function generateShortVipPackageId(): string {
  const randomStr = Math.random().toString(36).substring(2, 10);
  return `vip_${randomStr}`;
}

/**
 * 根据套餐名称和时长生成语义化ID
 * 格式: vip_{type}_{duration}d_随机4位
 * 例如: vip_monthly_30d_a1b2, vip_yearly_365d_c3d4
 */
export function generateSemanticVipPackageId(name: string, duration: number): string {
  // 根据时长判断类型
  let type = 'custom';
  if (duration <= 7) {
    type = 'weekly';
  } else if (duration <= 31) {
    type = 'monthly';
  } else if (duration <= 93) {
    type = 'quarterly';
  } else if (duration <= 366) {
    type = 'yearly';
  }
  
  // 生成4位随机字符串
  const randomStr = Math.random().toString(36).substring(2, 6);
  
  return `vip_${type}_${duration}d_${randomStr}`;
}

/**
 * 生成支付订单号
 * 格式: PAY_yyyymmddhhmmss_随机6位
 * 例如: PAY_20231201143022_a1b2c3
 */
export function generatePaymentOrderNo(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  
  // 生成6位随机字符串
  const randomStr = Math.random().toString(36).substring(2, 8);
  
  return `PAY_${year}${month}${day}${hour}${minute}${second}_${randomStr}`;
}

/**
 * 生成UUID（去掉连字符）
 * 例如: a1b2c3d4e5f6789012345678901234567890abcd
 */
export function generateUUID(): string {
  return uuidv4().replace(/-/g, '');
}

/**
 * 生成短UUID（取前12位）
 * 例如: a1b2c3d4e5f6
 */
export function generateShortUUID(): string {
  return uuidv4().replace(/-/g, '').substring(0, 12);
}

/**
 * 生成数字ID（8位）
 * 例如: 12345678
 */
export function generateNumericId(): string {
  return Math.floor(10000000 + Math.random() * 90000000).toString();
}

/**
 * 检查ID是否已存在的通用方法
 * @param checkFn 检查函数，返回Promise<boolean>，true表示ID已存在
 * @param generateFn ID生成函数
 * @param maxRetries 最大重试次数
 */
export async function generateUniqueId(
  checkFn: (id: string) => Promise<boolean>,
  generateFn: () => string,
  maxRetries: number = 10
): Promise<string> {
  for (let i = 0; i < maxRetries; i++) {
    const id = generateFn();
    const exists = await checkFn(id);
    if (!exists) {
      return id;
    }
  }
  
  throw new Error(`无法生成唯一ID，已重试${maxRetries}次`);
}
