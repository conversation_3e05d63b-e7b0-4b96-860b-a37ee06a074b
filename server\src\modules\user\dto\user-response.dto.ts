import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({ description: '用户的唯一ID（8位随机数字）', example: '12345678' })
  id: string;

  @ApiProperty({ description: '用户手机号', example: '13800138000', required: false })
  phone?: string;

  @ApiProperty({ description: '微信用户的openid', required: false })
  openid?: string;

  @ApiProperty({ description: '用户昵称', required: false })
  nickname?: string;

  @ApiProperty({ description: '用户头像URL', required: false })
  avatarUrl?: string;

  @ApiProperty({ description: '用户当前已开启的关卡数', example: 5 })
  unlockedLevels: number;

  @ApiProperty({ description: '用户已通关的关卡ID列表', type: [String] })
  completedLevelIds: string[];

  @ApiProperty({ description: '用户总游戏次数' })
  totalGames: number;

  @ApiProperty({ description: '用户总通关次数' })
  totalCompletions: number;

  @ApiProperty({ description: '最后游戏时间', example: '2024-01-01 12:00:00' })
  lastPlayTime: string;

  @ApiProperty({ description: 'VIP状态', example: false })
  isVip: boolean;

  @ApiProperty({ description: 'VIP过期时间', example: '2024-01-31 23:59:59', required: false })
  vipExpiresAt?: string;

  @ApiProperty({ description: '每日解锁限制次数', example: 15 })
  dailyUnlockLimit: number;

  @ApiProperty({ description: '当日解锁次数', example: 3 })
  dailyUnlockCount: number;

  @ApiProperty({ description: '当日是否已分享', example: false })
  dailyShared: boolean;

  @ApiProperty({ description: '最后游戏日期（YYYY-MM-DD）', example: '2025-06-19' })
  lastPlayDate: string;

  @ApiProperty({ description: '总分享次数', example: 5 })
  totalShares: number;

  @ApiProperty({ description: '用户注册时间', example: '2024-01-01 12:00:00' })
  createdAt: string;

  @ApiProperty({ description: '用户信息最后更新时间', example: '2024-01-01 12:00:00' })
  updatedAt: string;
}
