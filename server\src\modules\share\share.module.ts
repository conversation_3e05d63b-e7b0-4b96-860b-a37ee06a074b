import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ShareService } from './share.service';
import { ShareController } from './share.controller';
import { ShareConfig, ShareConfigSchema } from './entities/share-config.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ShareConfig.name, schema: ShareConfigSchema }
    ]),
  ],
  controllers: [ShareController],
  providers: [ShareService],
  exports: [ShareService],
})
export class ShareModule {}
