import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsNotEmpty, Min, IsBoolean } from 'class-validator';

// 创建支付订单请求
export class CreatePaymentDto {
  @ApiProperty({ description: '用户openid', example: 'wx_openid_123456' })
  @IsString()
  @IsNotEmpty({ message: 'openid不能为空' })
  openid: string;

  @ApiProperty({ description: '商品描述', example: 'VIP会员-月卡' })
  @IsString()
  @IsNotEmpty({ message: '商品描述不能为空' })
  description: string;

  @ApiProperty({ description: '商户订单号', example: 'ORDER_20231201_001' })
  @IsString()
  @IsNotEmpty({ message: '商户订单号不能为空' })
  out_trade_no: string;

  @ApiProperty({ description: '订单金额（分）', example: 100 })
  @IsNumber({}, { message: '订单金额必须是数字' })
  @Min(1, { message: '订单金额必须大于0' })
  total: number;

  @ApiProperty({ description: '商品详情', required: false })
  @IsOptional()
  @IsString()
  detail?: string;

  @ApiProperty({ description: '附加数据', required: false })
  @IsOptional()
  @IsString()
  attach?: string;
}

// 微信支付统一下单响应
export class WechatPayOrderResponse {
  @ApiProperty({ description: '预支付交易会话标识' })
  prepay_id: string;
}

// 小程序支付参数响应
export class MiniProgramPaymentResponse {
  @ApiProperty({ description: '小程序ID' })
  appId: string;

  @ApiProperty({ description: '时间戳' })
  timeStamp: string;

  @ApiProperty({ description: '随机字符串' })
  nonceStr: string;

  @ApiProperty({ description: '订单详情扩展字符串' })
  package: string;

  @ApiProperty({ description: '签名方式' })
  signType: string;

  @ApiProperty({ description: '签名' })
  paySign: string;
}

// 支付结果通知
export class PaymentNotifyDto {
  @ApiProperty({ description: '通知ID' })
  id: string;

  @ApiProperty({ description: '通知创建时间' })
  create_time: string;

  @ApiProperty({ description: '通知类型' })
  event_type: string;

  @ApiProperty({ description: '通知数据类型' })
  resource_type: string;

  @ApiProperty({ description: '通知资源数据' })
  resource: {
    original_type: string;
    algorithm: string;
    ciphertext: string;
    associated_data: string;
    nonce: string;
  };

  @ApiProperty({ description: '回调摘要' })
  summary: string;
}

// 支付订单查询响应
export class PaymentQueryResponse {
  @ApiProperty({ description: '应用ID' })
  appid: string;

  @ApiProperty({ description: '商户号' })
  mchid: string;

  @ApiProperty({ description: '商户订单号' })
  out_trade_no: string;

  @ApiProperty({ description: '微信支付订单号' })
  transaction_id: string;

  @ApiProperty({ description: '交易类型' })
  trade_type: string;

  @ApiProperty({ description: '交易状态' })
  trade_state: string;

  @ApiProperty({ description: '交易状态描述' })
  trade_state_desc: string;

  @ApiProperty({ description: '付款银行' })
  bank_type: string;

  @ApiProperty({ description: '支付完成时间' })
  success_time: string;

  @ApiProperty({ description: '用户标识' })
  payer: {
    openid: string;
  };

  @ApiProperty({ description: '订单金额' })
  amount: {
    total: number;
    payer_total: number;
    currency: string;
    payer_currency: string;
  };
}

// VIP套餐定义
export class VipPackageDto {
  @ApiProperty({ description: '套餐ID', example: 'vip_monthly' })
  id: string;

  @ApiProperty({ description: '套餐名称', example: 'VIP月卡' })
  name: string;

  @ApiProperty({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡' })
  description: string;

  @ApiProperty({ description: '价格（分）', example: 2900 })
  price: number;

  @ApiProperty({ description: '有效期（天）', example: 30 })
  duration: number;

  @ApiProperty({ description: '排序权重', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '是否启用', example: true })
  isActive: boolean;

  @ApiProperty({ description: '创建时间' })
  createdAt: string;

  @ApiProperty({ description: '更新时间' })
  updatedAt: string;
}

// 创建VIP套餐DTO
export class CreateVipPackageDto {
  @ApiProperty({ description: '套餐名称', example: 'VIP月卡' })
  @IsString()
  @IsNotEmpty({ message: '套餐名称不能为空' })
  name: string;

  @ApiProperty({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡' })
  @IsString()
  @IsNotEmpty({ message: '套餐描述不能为空' })
  description: string;

  @ApiProperty({ description: '价格（分）', example: 2900 })
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(1, { message: '价格必须大于0' })
  price: number;

  @ApiProperty({ description: '有效期（天）', example: 30 })
  @IsNumber({}, { message: '有效期必须是数字' })
  @Min(1, { message: '有效期必须大于0' })
  duration: number;

  @ApiProperty({ description: '排序权重', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序权重必须是数字' })
  @Min(1, { message: '排序权重必须大于0' })
  sortOrder?: number;

  @ApiProperty({ description: '是否启用', example: true, required: false })
  @IsOptional()
  @IsBoolean({ message: '启用状态必须是布尔值' })
  isActive?: boolean;
}

// 更新VIP套餐DTO
export class UpdateVipPackageDto {
  @ApiProperty({ description: '套餐名称', example: 'VIP月卡', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '套餐名称不能为空' })
  name?: string;

  @ApiProperty({ description: '套餐描述', example: '30天VIP特权，无限制解锁关卡', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '套餐描述不能为空' })
  description?: string;

  @ApiProperty({ description: '价格（分）', example: 2900, required: false })
  @IsOptional()
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(1, { message: '价格必须大于0' })
  price?: number;

  @ApiProperty({ description: '有效期（天）', example: 30, required: false })
  @IsOptional()
  @IsNumber({}, { message: '有效期必须是数字' })
  @Min(1, { message: '有效期必须大于0' })
  duration?: number;

  @ApiProperty({ description: '排序权重', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序权重必须是数字' })
  @Min(1, { message: '排序权重必须大于0' })
  sortOrder?: number;

  @ApiProperty({ description: '是否启用', example: true, required: false })
  @IsOptional()
  @IsBoolean({ message: '启用状态必须是布尔值' })
  isActive?: boolean;
}

// 支付订单状态枚举
export enum PaymentStatus {
  PENDING = 'PENDING',     // 待支付
  SUCCESS = 'SUCCESS',     // 支付成功
  FAILED = 'FAILED',       // 支付失败
  CANCELLED = 'CANCELLED', // 已取消
  REFUNDED = 'REFUNDED',   // 已退款
}

// 支付订单实体
export class PaymentOrderDto {
  @ApiProperty({ description: '订单ID' })
  id: string;

  @ApiProperty({ description: '用户ID' })
  userId: string;

  @ApiProperty({ description: '用户openid' })
  openid: string;

  @ApiProperty({ description: '商户订单号' })
  out_trade_no: string;

  @ApiProperty({ description: '微信支付订单号' })
  transaction_id?: string;

  @ApiProperty({ description: '商品描述' })
  description: string;

  @ApiProperty({ description: '订单金额（分）' })
  total: number;

  @ApiProperty({ description: '支付状态', enum: PaymentStatus })
  status: PaymentStatus;

  @ApiProperty({ description: 'VIP套餐ID' })
  vip_package_id: string;

  @ApiProperty({ description: '预支付ID' })
  prepay_id?: string;

  @ApiProperty({ description: '支付完成时间' })
  paid_at?: string;

  @ApiProperty({ description: '创建时间' })
  created_at: string;

  @ApiProperty({ description: '更新时间' })
  updated_at: string;
}
