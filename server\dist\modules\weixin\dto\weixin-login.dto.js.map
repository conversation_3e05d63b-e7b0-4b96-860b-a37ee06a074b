{"version": 3, "file": "weixin-login.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/weixin/dto/weixin-login.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAA4E;AAE5E,MAAa,cAAc;IAIzB,IAAI,CAAS;IAOb,KAAK,CAAU;IAKf,QAAQ,CAAU;IAKlB,SAAS,CAAU;CACpB;AAtBD,wCAsBC;AAlBC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IAC1F,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;4CACvB;AAOb;IALC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACrC,IAAA,yBAAO,EAAC,eAAe,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;6CACrC;AAKf;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACO;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,qCAAqC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC5G,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACQ;AAGrB,MAAa,sBAAsB;IAEjC,MAAM,CAA0B;IAGhC,OAAO,CAAS;IAGhB,MAAM,CAAS;IAGf,UAAU,CAAU;IAGpB,QAAQ,CAWN;IAGF,OAAO,CAAU;CAClB;AA7BD,wDA6BC;AA3BC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;sDACzB;AAGhC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;uDACtC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;;sDACnE;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;;0DAC7D;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;wDAY7D;AAGF;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;uDACtD;AAGnB,MAAa,kBAAkB;IAI7B,MAAM,CAAS;IAKf,KAAK,CAAS;IAKd,QAAQ,CAAU;IAKlB,SAAS,CAAU;CACpB;AApBD,gDAoBC;AAhBC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACnF,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;kDACvB;AAKf;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;IAC7D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;iDACrB;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACO;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,qCAAqC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC5G,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACQ;AAGrB,MAAa,qBAAqB;IAIhC,MAAM,CAAS;IAKf,UAAU,CAAS;CACpB;AAVD,sDAUC;AANC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACnF,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;qDACvB;AAKf;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAChE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;;yDACvB"}