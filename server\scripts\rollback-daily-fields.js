/**
 * 数据库回滚脚本：将每日解锁限制字段回滚为每日通关限制字段
 * 
 * 回滚内容：
 * - dailyUnlockLimit -> dailyPlayLimit
 * - dailyUnlockCount -> dailyPlayCount
 * 
 * 运行方式：
 * node scripts/rollback-daily-fields.js
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || '**************************************************';
const DATABASE_NAME = process.env.DATABASE_NAME || 'vocabulary-game';

async function rollbackDailyFields() {
  let client;
  
  try {
    console.log('🔄 开始数据库回滚...');
    console.log(`📍 连接数据库: ${MONGODB_URI}`);
    
    // 连接数据库
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const usersCollection = db.collection('users');
    
    // 检查集合是否存在
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      console.log('⚠️  用户集合不存在，跳过回滚');
      return;
    }
    
    // 获取所有用户
    const users = await usersCollection.find({}).toArray();
    console.log(`📊 找到 ${users.length} 个用户需要回滚`);
    
    if (users.length === 0) {
      console.log('✅ 没有用户数据需要回滚');
      return;
    }
    
    let rolledBackCount = 0;
    let skippedCount = 0;
    
    // 批量更新操作
    const bulkOps = [];
    
    for (const user of users) {
      const updateFields = {};
      let needsUpdate = false;
      
      // 检查是否需要回滚 dailyUnlockLimit -> dailyPlayLimit
      if (user.hasOwnProperty('dailyUnlockLimit') && !user.hasOwnProperty('dailyPlayLimit')) {
        updateFields.dailyPlayLimit = user.dailyUnlockLimit || 15;
        needsUpdate = true;
      }
      
      // 检查是否需要回滚 dailyUnlockCount -> dailyPlayCount
      if (user.hasOwnProperty('dailyUnlockCount') && !user.hasOwnProperty('dailyPlayCount')) {
        updateFields.dailyPlayCount = user.dailyUnlockCount || 0;
        needsUpdate = true;
      }
      
      // 确保旧字段存在（为没有新字段的用户设置默认值）
      if (!user.hasOwnProperty('dailyPlayLimit')) {
        updateFields.dailyPlayLimit = 15;
        needsUpdate = true;
      }
      
      if (!user.hasOwnProperty('dailyPlayCount')) {
        updateFields.dailyPlayCount = 0;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        bulkOps.push({
          updateOne: {
            filter: { _id: user._id },
            update: { $set: updateFields }
          }
        });
        rolledBackCount++;
      } else {
        skippedCount++;
      }
    }
    
    // 执行批量更新
    if (bulkOps.length > 0) {
      console.log(`🔄 执行批量回滚，共 ${bulkOps.length} 个操作...`);
      const result = await usersCollection.bulkWrite(bulkOps);
      console.log(`✅ 批量回滚完成: 修改了 ${result.modifiedCount} 个文档`);
    }
    
    // 清理新字段（可选，谨慎操作）
    console.log('🧹 开始清理新字段...');
    const cleanupResult = await usersCollection.updateMany(
      {},
      { 
        $unset: { 
          dailyUnlockLimit: "",
          dailyUnlockCount: ""
        } 
      }
    );
    console.log(`🗑️  清理新字段完成: 修改了 ${cleanupResult.modifiedCount} 个文档`);
    
    // 验证回滚结果
    console.log('🔍 验证回滚结果...');
    const verificationUsers = await usersCollection.find({}).limit(5).toArray();
    
    for (const user of verificationUsers) {
      console.log(`👤 用户 ${user.id || user._id}:`);
      console.log(`   - dailyPlayLimit: ${user.dailyPlayLimit}`);
      console.log(`   - dailyPlayCount: ${user.dailyPlayCount}`);
      console.log(`   - isVip: ${user.isVip}`);
      
      // 检查是否还有新字段
      if (user.hasOwnProperty('dailyUnlockLimit') || user.hasOwnProperty('dailyUnlockCount')) {
        console.log(`   ⚠️  仍存在新字段`);
      }
    }
    
    console.log('\n📈 回滚统计:');
    console.log(`   - 总用户数: ${users.length}`);
    console.log(`   - 已回滚: ${rolledBackCount}`);
    console.log(`   - 已跳过: ${skippedCount}`);
    console.log('✅ 数据库回滚完成！');
    
  } catch (error) {
    console.error('❌ 回滚过程中发生错误:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  try {
    // 确认回滚操作
    console.log('⚠️  警告：此操作将回滚数据库字段更改');
    console.log('   - dailyUnlockLimit -> dailyPlayLimit');
    console.log('   - dailyUnlockCount -> dailyPlayCount');
    console.log('');
    
    // 在生产环境中，可以添加确认提示
    // const readline = require('readline');
    // const rl = readline.createInterface({
    //   input: process.stdin,
    //   output: process.stdout
    // });
    // 
    // const answer = await new Promise(resolve => {
    //   rl.question('确定要继续回滚吗？(yes/no): ', resolve);
    // });
    // rl.close();
    // 
    // if (answer.toLowerCase() !== 'yes') {
    //   console.log('❌ 回滚操作已取消');
    //   return;
    // }
    
    await rollbackDailyFields();
    process.exit(0);
  } catch (error) {
    console.error('💥 回滚失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { rollbackDailyFields };
