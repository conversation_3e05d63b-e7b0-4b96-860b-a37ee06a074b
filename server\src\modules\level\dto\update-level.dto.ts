import { PartialType, ApiProperty } from '@nestjs/swagger';
import { CreateLevelDto } from './create-level.dto';
import { IsArray, IsOptional, IsUUID } from 'class-validator';

export class UpdateLevelDto extends PartialType(CreateLevelDto) {
  @ApiProperty({ description: '更新关联的词库ID列表', type: [String], example: ['uuid3-thesaurus-zzz'], required: false })
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true, message: '每个词库ID都必须是有效的UUID格式' })
  thesaurusIds?: string[];

  @ApiProperty({ description: '更新直接选择的词组ID列表', type: [String], example: ['uuid1-phrase-xxx', 'uuid2-phrase-yyy'], required: false})
  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true, message: '每个词组ID都必须是有效的UUID格式' })
  phraseIds?: string[];
}