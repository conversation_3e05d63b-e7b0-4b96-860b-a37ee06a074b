import { CreateUserDto } from './create-user.dto';
declare const UpdateUserDto_base: import("@nestjs/mapped-types").MappedType<Partial<CreateUserDto>>;
export declare class UpdateUserDto extends UpdateUserDto_base {
    unlockedLevels?: number;
    completedLevelIds?: string[];
    totalGames?: number;
    totalCompletions?: number;
    isVip?: boolean;
    vipExpiresAt?: Date;
    dailyUnlockLimit?: number;
    dailyUnlockCount?: number;
    dailyShared?: boolean;
    lastPlayDate?: string;
    totalShares?: number;
}
export {};
