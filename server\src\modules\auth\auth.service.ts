import { Injectable, UnauthorizedException, OnModuleInit, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { LoginAdminDto } from './dto/login-admin.dto';
import { AdminUser, AdminUserDocument } from './entities/admin-user.entity';

@Injectable()
export class AuthService implements OnModuleInit {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectModel(AdminUser.name) private adminUserModel: Model<AdminUserDocument>,
    private readonly jwtService: JwtService,
  ) {}

  // 应用启动时初始化默认管理员账户
  async onModuleInit() {
    await this.initializeDefaultAdmin();
  }

  // 初始化默认管理员账户
  private async initializeDefaultAdmin() {
    try {
      const existingAdmin = await this.adminUserModel.findOne({ username: 'admin' }).exec();

      if (!existingAdmin) {
        const hashedPassword = await bcrypt.hash('password123', 10);

        const defaultAdmin = new this.adminUserModel({
          userId: 'admin001',
          username: 'admin',
          password: hashedPassword,
          roles: ['admin'],
          isActive: true,
        });

        await defaultAdmin.save();
        this.logger.log('✅ 默认管理员账户创建成功: admin/password123');
      } else {
        this.logger.log('📝 默认管理员账户已存在');
      }
    } catch (error) {
      this.logger.error('❌ 初始化默认管理员账户失败:', error);
    }
  }

  async login(loginAdminDto: LoginAdminDto) {
    const { username, password } = loginAdminDto;

    // 从数据库查询用户
    const user = await this.adminUserModel.findOne({
      username,
      isActive: true
    }).exec();

    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    await user.save();

    const payload = {
      username: user.username,
      sub: user.userId,
      roles: user.roles
    };

    this.logger.log(`👤 用户登录成功: ${username}`);

    return {
      message: '登录成功',
      accessToken: this.jwtService.sign(payload),
      user: {
        userId: user.userId,
        username: user.username,
        roles: user.roles,
      },
    };
  }

  // 根据用户名查找用户（用于JWT策略验证）
  async findUserByUsername(username: string): Promise<AdminUserDocument | null> {
    return this.adminUserModel.findOne({
      username,
      isActive: true
    }).exec();
  }

  // 根据用户ID查找用户
  async findUserById(userId: string): Promise<AdminUserDocument | null> {
    return this.adminUserModel.findOne({
      userId,
      isActive: true
    }).exec();
  }

  // 创建新的管理员用户
  async createAdminUser(userData: {
    userId: string;
    username: string;
    password: string;
    roles?: string[];
  }): Promise<AdminUserDocument> {
    const hashedPassword = await bcrypt.hash(userData.password, 10);

    const newUser = new this.adminUserModel({
      ...userData,
      password: hashedPassword,
      roles: userData.roles || ['admin'],
      isActive: true,
    });

    return newUser.save();
  }
}