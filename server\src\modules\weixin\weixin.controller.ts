import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { UserService } from '../user/user.service';
import { LevelService } from '../level/level.service';
import { WeixinUserBindDto } from './dto/weixin-user-bind.dto';
import { WeixinUserInfoDto, WeixinLevelDto } from './dto/weixin-user-info.dto';
import { WeixinLevelDetailDto } from './dto/weixin-level-detail.dto';
import {
  WeixinCompleteLevelDto,
  WeixinCompleteLevelResponseDto,
} from './dto/weixin-complete-level.dto';
import {
  WeixinLoginDto,
  WeixinLoginResponseDto,
  WeixinPhoneBindDto,
  WeixinSessionCheckDto,
} from './dto/weixin-login.dto';
import {
  WeixinShareDto,
  WeixinShareResponseDto,
  WeixinDailyStatusDto,
} from './dto/weixin-daily-play.dto';
import {
  WeixinShareConfigDto,
  WeixinShareConfigListDto,
} from './dto/weixin-share-config.dto';
import { WeixinAppSettingsDto } from './dto/weixin-settings.dto';
import { WeixinGlobalConfigDto } from './dto/weixin-global-config.dto';
import { WeixinApiService } from './services/weixin-api.service';
import { ShareService } from '../share/share.service';
import { PaymentService } from '../payment/payment.service';
import { SettingsService } from '../settings/settings.service';
import {
  MiniProgramPaymentResponse,
  VipPackageDto,
} from '../payment/dto/payment.dto';

@ApiTags('weixin')
@Controller('weixin')
export class WeixinController {
  constructor(
    private readonly userService: UserService,
    private readonly levelService: LevelService,
    private readonly weixinApiService: WeixinApiService,
    private readonly shareService: ShareService,
    private readonly paymentService: PaymentService,
    private readonly settingsService: SettingsService,
  ) {}

  @Post('login')
  @ApiOperation({ summary: '微信小程序登录' })
  @ApiResponse({
    status: 201,
    description: '登录成功或需要绑定手机号',
    type: WeixinLoginResponseDto,
    schema: {
      examples: {
        loginSuccess: {
          summary: '登录成功',
          value: {
            status: 'success',
            message: '登录成功',
            openid: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE',
            userInfo: {
              id: '12345678',
              maskedPhone: '138****8000', // 可选字段，如果用户没有绑定手机号则为undefined
              nickname: '微信用户',
              avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/xxx',
              unlockedLevels: 5,
              completedLevelIds: ['1', '2', '3'],
              totalGames: 20,
              totalCompletions: 15,
              lastPlayTime: '2025-06-18T12:00:00.000Z',
              createdAt: '2025-06-18T10:00:00.000Z',
            },
          },
        },
        needBind: {
          summary: '需要绑定手机号',
          value: {
            status: 'need_bind',
            message: '请绑定手机号完成注册',
            openid: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '登录凭证无效或微信接口调用失败',
    schema: {
      examples: {
        invalidCode: {
          summary: '登录凭证无效',
          value: {
            statusCode: 400,
            message:
              '微信登录失败: 登录凭证无效或已过期，请重新登录 (错误码: 40029)',
            error: 'Bad Request',
          },
        },
        configError: {
          summary: '配置错误',
          value: {
            statusCode: 400,
            message:
              '微信登录失败: AppID无效，请检查小程序配置 (错误码: 40013)',
            error: 'Bad Request',
          },
        },
      },
    },
  })
  async login(
    @Body() loginDto: WeixinLoginDto,
  ): Promise<WeixinLoginResponseDto> {
    try {
      // 记录登录尝试
      console.log(
        `🔐 微信登录尝试: code=${loginDto.code?.substring(0, 8)}..., phone=${loginDto.phone ? '已提供' : '未提供'}, nickname=${loginDto.nickname || '未提供'}`,
      );

      // 调用微信接口获取用户信息
      const weixinData = await this.weixinApiService.code2Session(
        loginDto.code,
      );

      // 转换数据格式以匹配UserService期望的参数
      const loginData = {
        openid: weixinData.openid,
        sessionKey: weixinData.session_key,
        unionid: weixinData.unionid,
      };

      // 处理登录逻辑
      const result = await this.userService.weixinLogin(loginDto, loginData);

      console.log(
        `✅ 微信登录成功: openid=${weixinData.openid.substring(0, 8)}..., status=${result.status}`,
      );
      return result;
    } catch (error) {
      console.error(`❌ 微信登录失败: ${error.message}`);
      throw error;
    }
  }

  @Post('bind-phone')
  @ApiOperation({ summary: '微信用户绑定手机号' })
  @ApiResponse({
    status: 201,
    description: '绑定成功',
    type: WeixinLoginResponseDto,
    schema: {
      example: {
        status: 'success',
        message: '绑定成功',
        openid: 'oGZUI0egBJY1zhBYw2KhdUfwVJJE',
        userInfo: {
          id: '12345678',
          maskedPhone: '138****8000',
          nickname: '微信用户',
          avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/xxx',
          unlockedLevels: 1,
          completedLevelIds: [],
          totalGames: 0,
          totalCompletions: 0,
          lastPlayTime: '2025-06-18T12:00:00.000Z',
          createdAt: '2025-06-18T12:00:00.000Z',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: '手机号已被使用或openid已绑定用户' })
  async bindPhone(
    @Body() bindDto: WeixinPhoneBindDto,
  ): Promise<WeixinLoginResponseDto> {
    return this.userService.weixinBindPhone(bindDto);
  }

  @Get('config')
  @ApiOperation({ summary: '获取微信小程序配置状态（调试用）' })
  @ApiResponse({
    status: 200,
    description: '配置状态',
    schema: {
      example: {
        appId: 'wx1234567890abcdef...',
        appSecret: '已配置',
        isConfigured: true,
        paymentOrderExpireMinutes: 30,
        timestamp: '2025-06-21T10:30:00.000Z',
        environment: 'development',
      },
    },
  })
  async getConfig() {
    const config = this.weixinApiService.getConfig();
    return {
      ...config,
      paymentOrderExpireMinutes:
        this.paymentService.getPaymentOrderExpireMinutes(),
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
    };
  }

  @Get('global-config')
  @ApiOperation({ summary: '获取微信小程序全局配置' })
  @ApiResponse({
    status: 200,
    description: '全局配置获取成功',
    type: WeixinGlobalConfigDto,
    schema: {
      example: {
        appId: 'wx1234567890abcdef',
        isConfigured: true,
        paymentOrderExpireMinutes: 30,
        environment: 'development',
        timestamp: '2024-01-01T12:00:00.000Z',
        helpUrl: 'https://help.example.com',
        backgroundMusicUrl: 'https://music.example.com/background.mp3',
        version: '1.0.0',
        features: {
          enablePayment: true,
          enableShare: true,
          enableVip: true,
          enableMusic: true,
        },
        gameConfig: {
          maxLevels: 1000,
          dailyUnlockLimit: 15,
          shareRewardCount: 5,
        },
      },
    },
  })
  @ApiResponse({ status: 500, description: '获取配置失败' })
  async getGlobalConfig(): Promise<WeixinGlobalConfigDto> {
    try {
      // 获取微信配置
      const weixinConfig = this.weixinApiService.getConfig();

      // 获取应用设置
      const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([
        this.settingsService.findByKey('help_url').catch(() => null),
        this.settingsService
          .findByKey('background_music_url')
          .catch(() => null),
      ]);

      // 构建全局配置
      const globalConfig: WeixinGlobalConfigDto = {
        // 微信配置
        appId: weixinConfig.appId,
        isConfigured: weixinConfig.isConfigured,
        paymentOrderExpireMinutes:
          this.paymentService.getPaymentOrderExpireMinutes(),
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),

        // 应用设置
        helpUrl: helpUrlSetting?.value || 'https://help.example.com',
        backgroundMusicUrl:
          backgroundMusicSetting?.value ||
          'https://music.example.com/background.mp3',

        // 应用版本
        version: process.env.APP_VERSION || '1.0.0',

        // 功能开关
        features: {
          enablePayment: weixinConfig.isConfigured,
          enableShare: true,
          enableVip: true,
          enableMusic: true,
        },

        // 游戏配置
        gameConfig: {
          maxLevels: 1000,
          dailyUnlockLimit: 15,
          shareRewardCount: 5,
        },
      };

      return globalConfig;
    } catch (error) {
      console.error('获取全局配置失败:', error);
      // 返回默认配置
      return {
        appId: '',
        isConfigured: false,
        paymentOrderExpireMinutes: 30,
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        helpUrl: 'https://help.example.com',
        backgroundMusicUrl: 'https://music.example.com/background.mp3',
        version: '1.0.0',
        features: {
          enablePayment: false,
          enableShare: true,
          enableVip: true,
          enableMusic: true,
        },
        gameConfig: {
          maxLevels: 1000,
          dailyUnlockLimit: 15,
          shareRewardCount: 5,
        },
      };
    }
  }

  @Get('app-settings')
  @ApiOperation({ summary: '获取小程序应用设置' })
  @ApiResponse({
    status: 200,
    description: '应用设置获取成功',
    type: WeixinAppSettingsDto,
    schema: {
      example: {
        helpUrl: 'https://help.example.com',
        backgroundMusicUrl: 'https://music.example.com/background.mp3',
      },
    },
  })
  @ApiResponse({ status: 500, description: '获取设置失败' })
  async getAppSettings(): Promise<WeixinAppSettingsDto> {
    try {
      const [helpUrlSetting, backgroundMusicSetting] = await Promise.all([
        this.settingsService.findByKey('help_url').catch(() => null),
        this.settingsService
          .findByKey('background_music_url')
          .catch(() => null),
      ]);

      return {
        helpUrl: helpUrlSetting?.value || 'https://help.example.com',
        backgroundMusicUrl:
          backgroundMusicSetting?.value ||
          'https://music.example.com/background.mp3',
      };
    } catch (error) {
      // 如果获取失败，返回默认值
      return {
        helpUrl: 'https://help.example.com',
        backgroundMusicUrl: 'https://music.example.com/background.mp3',
      };
    }
  }

  @Post('user/bind')
  @ApiOperation({ summary: '微信小程序用户绑定' })
  @ApiResponse({
    status: 201,
    description: '用户绑定成功',
    type: WeixinUserInfoDto,
    schema: {
      example: {
        id: '12345678',
        maskedPhone: '138****8000',
        nickname: '微信用户',
        avatarUrl: 'https://wx.qlogo.cn/mmopen/...',
        unlockedLevels: 1,
        completedLevelIds: [],
        totalGames: 0,
        totalCompletions: 0,
        lastPlayTime: '2025-06-18T12:00:00.000Z',
        createdAt: '2025-06-18T12:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 400, description: '手机号或openid已存在，或参数无效' })
  async bindUser(
    @Body() bindDto: WeixinUserBindDto,
  ): Promise<WeixinUserInfoDto> {
    return this.userService.bindWeixinUser(bindDto);
  }

  @Get('user/info')
  @ApiOperation({ summary: '根据openid获取微信小程序用户信息' })
  @ApiQuery({
    name: 'openid',
    description: '微信用户openid',
    example: 'wx_openid_123456',
  })
  @ApiResponse({
    status: 200,
    description: '用户信息获取成功',
    type: WeixinUserInfoDto,
    schema: {
      example: {
        id: '12345678',
        maskedPhone: '138****8000',
        nickname: '微信用户',
        avatarUrl: 'https://wx.qlogo.cn/mmopen/...',
        unlockedLevels: 5,
        completedLevelIds: ['1', '2', '3'],
        totalGames: 20,
        totalCompletions: 15,
        lastPlayTime: '2025-06-18T12:00:00.000Z',
        createdAt: '2025-06-18T10:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getUserInfo(
    @Query('openid') openid: string,
  ): Promise<WeixinUserInfoDto> {
    return this.userService.getWeixinUserInfo(openid);
  }

  @Get('levels')
  @ApiOperation({ summary: '获取微信小程序关卡列表（包含用户进度）' })
  @ApiQuery({
    name: 'openid',
    description: '微信用户openid',
    example: 'wx_openid_123456',
  })
  @ApiResponse({
    status: 200,
    description: '关卡列表获取成功',
    type: [WeixinLevelDto],
    schema: {
      example: [
        {
          id: 'level-uuid-1',
          name: '第1关 - 基础词汇',
          difficulty: 1,
          description: '这是第一关，包含基础词汇',
          isUnlocked: true,
          isCompleted: true,
          createdAt: '2025-06-18T10:00:00.000Z',
        },
        {
          id: 'level-uuid-2',
          name: '第2关 - 进阶词汇',
          difficulty: 2,
          description: '这是第二关，包含进阶词汇',
          isUnlocked: true,
          isCompleted: false,
          createdAt: '2025-06-18T10:00:00.000Z',
        },
        {
          id: 'level-uuid-3',
          name: '第3关 - 高级词汇',
          difficulty: 3,
          description: '这是第三关，包含高级词汇',
          isUnlocked: false,
          isCompleted: false,
          createdAt: '2025-06-18T10:00:00.000Z',
        },
      ],
    },
  })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getLevelsWithProgress(
    @Query('openid') openid: string,
  ): Promise<WeixinLevelDto[]> {
    return this.userService.getWeixinLevelsWithProgress(openid);
  }

  @Get('user/:id/info')
  @ApiOperation({ summary: '根据用户ID获取微信小程序用户信息' })
  @ApiParam({ name: 'id', description: '用户ID', example: '12345678' })
  @ApiResponse({
    status: 200,
    description: '用户信息获取成功',
    type: WeixinUserInfoDto,
  })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getUserInfoById(@Param('id') id: string): Promise<WeixinUserInfoDto> {
    return this.userService.getWeixinUserInfoById(id);
  }

  @Get('level/:levelId')
  @ApiOperation({ summary: '根据关卡ID获取微信小程序关卡详情（包含词组信息）' })
  @ApiParam({ name: 'levelId', description: '关卡ID', example: 'level-uuid-1' })
  @ApiQuery({
    name: 'openid',
    description: '微信用户openid',
    example: 'wx_openid_123456',
  })
  @ApiResponse({
    status: 200,
    description: '关卡详情获取成功',
    type: WeixinLevelDetailDto,
    schema: {
      example: {
        id: 'level-uuid-1',
        name: '第1关 - 基础词汇',
        difficulty: 1,
        description: '这是第一关，包含基础词汇',
        isUnlocked: true,
        isCompleted: false,
        phrases: [
          {
            id: 'phrase-uuid-1',
            text: 'Hello World',
            meaning: '你好，世界',
            exampleSentence:
              'When you start programming, the first thing you often do is print "Hello World".',
            tags: ['greeting', 'common'],
          },
          {
            id: 'phrase-uuid-2',
            text: 'Good Morning',
            meaning: '早上好',
            exampleSentence: 'Good morning! How are you today?',
            tags: ['greeting', 'daily'],
          },
        ],
        createdAt: '2025-06-18T10:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 404, description: '用户不存在或关卡不存在' })
  @ApiResponse({ status: 400, description: '关卡尚未解锁' })
  async getLevelDetail(
    @Param('levelId') levelId: string,
    @Query('openid') openid: string,
  ): Promise<WeixinLevelDetailDto> {
    return this.userService.getWeixinLevelDetail(openid, levelId);
  }

  @Post('level/complete')
  @ApiOperation({ summary: '微信小程序用户通关关卡' })
  @ApiResponse({
    status: 201,
    description: '关卡通关成功',
    type: WeixinCompleteLevelResponseDto,
    schema: {
      example: {
        message: '恭喜！关卡通关成功，解锁新关卡！',
        userId: '12345678',
        levelId: 'level-uuid-1',
        unlockedLevels: 2,
        totalCompletions: 1,
        hasUnlockedNewLevel: true,
      },
    },
  })
  @ApiResponse({ status: 400, description: '关卡已完成、尚未解锁或参数无效' })
  @ApiResponse({ status: 404, description: '用户不存在或关卡不存在' })
  async completeLevel(
    @Body() completeLevelDto: WeixinCompleteLevelDto,
  ): Promise<WeixinCompleteLevelResponseDto> {
    return this.userService.weixinCompleteLevel(completeLevelDto);
  }

  @Get('daily-status')
  @ApiOperation({ summary: '获取用户每日通关状态' })
  @ApiQuery({
    name: 'openid',
    description: '微信用户openid',
    example: 'wx_openid_123456',
  })
  @ApiResponse({
    status: 200,
    description: '每日状态获取成功',
    type: WeixinDailyStatusDto,
    schema: {
      example: {
        id: '12345678',
        dailyPlayCount: 3,
        dailyPlayLimit: 15,
        remainingPlays: 12,
        dailyShared: false,
        isVip: false,
        lastPlayDate: '2025-06-19',
        totalShares: 5,
        canPlay: true,
      },
    },
  })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getDailyStatus(
    @Query('openid') openid: string,
  ): Promise<WeixinDailyStatusDto> {
    return this.userService.getWeixinDailyStatus(openid);
  }

  @Post('share')
  @ApiOperation({ summary: '用户分享获得额外通关机会' })
  @ApiResponse({
    status: 201,
    description: '分享成功或已分享',
    type: WeixinShareResponseDto,
    schema: {
      examples: {
        shareSuccess: {
          summary: '分享成功',
          value: {
            status: 'success',
            message: '分享成功，获得5次额外通关机会！',
            userId: '12345678',
            dailyPlayCount: 3,
            dailyPlayLimit: 20,
            remainingPlays: 17,
            isVip: false,
            totalShares: 6,
          },
        },
        alreadyShared: {
          summary: '今日已分享',
          value: {
            status: 'already_shared',
            message: '今日已分享过，无法重复获得奖励',
            userId: '12345678',
            dailyPlayCount: 3,
            dailyPlayLimit: 15,
            remainingPlays: 12,
            isVip: false,
            totalShares: 5,
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async share(
    @Body() shareDto: WeixinShareDto,
  ): Promise<WeixinShareResponseDto> {
    return this.userService.weixinShare(shareDto);
  }

  @Get('share-config')
  @ApiOperation({ summary: '获取微信小程序分享配置' })
  @ApiResponse({
    status: 200,
    description: '分享配置获取成功',
    type: WeixinShareConfigListDto,
    schema: {
      example: {
        default: {
          title: '一起来挑战词汇游戏！',
          path: '/pages/index/index',
          imageUrl: 'https://example.com/share.jpg',
          description: '挑战你的词汇量，看看你能通过多少关！',
          type: 'default',
        },
        configs: [
          {
            title: '我在词汇游戏中获得了高分！',
            path: '/pages/result/result',
            imageUrl: 'https://example.com/result-share.jpg',
            description: '快来挑战我的记录吧！',
            type: 'result',
          },
        ],
        total: 2,
      },
    },
  })
  async getShareConfig(): Promise<WeixinShareConfigListDto> {
    const activeConfigs = await this.shareService.findActive();

    // 查找默认配置
    const defaultConfig = activeConfigs.find(
      (config) => config.type === 'default',
    );
    const otherConfigs = activeConfigs.filter(
      (config) => config.type !== 'default',
    );

    // 转换为微信小程序格式
    const mapToWeixinConfig = (config: any): WeixinShareConfigDto => ({
      title: config.title,
      path: config.path,
      imageUrl: config.imageUrl,
      description: config.description,
      type: config.type,
    });

    return {
      default: defaultConfig
        ? mapToWeixinConfig(defaultConfig)
        : {
            title: '一起来挑战词汇游戏！',
            path: '/pages/index/index',
            description: '挑战你的词汇量，看看你能通过多少关！',
            type: 'default',
          },
      configs: otherConfigs.map(mapToWeixinConfig),
      total: activeConfigs.length,
    };
  }

  @Get('share-config/:type')
  @ApiOperation({ summary: '根据类型获取微信小程序分享配置' })
  @ApiParam({ name: 'type', description: '分享类型', example: 'default' })
  @ApiResponse({
    status: 200,
    description: '分享配置获取成功',
    type: WeixinShareConfigDto,
    schema: {
      example: {
        title: '一起来挑战词汇游戏！',
        path: '/pages/index/index',
        imageUrl: 'https://example.com/share.jpg',
        description: '挑战你的词汇量，看看你能通过多少关！',
        type: 'default',
      },
    },
  })
  @ApiResponse({ status: 404, description: '指定类型的分享配置不存在' })
  async getShareConfigByType(
    @Param('type') type: string,
  ): Promise<WeixinShareConfigDto | null> {
    const config = await this.shareService.findByType(type);

    if (!config) {
      return null;
    }

    return {
      title: config.title,
      path: config.path,
      imageUrl: config.imageUrl,
      description: config.description,
      type: config.type,
    };
  }

  @Get('vip-packages')
  @ApiOperation({ summary: '获取VIP套餐列表' })
  @ApiResponse({
    status: 200,
    description: 'VIP套餐列表获取成功',
    type: [VipPackageDto],
    schema: {
      example: [
        {
          id: 'vip_monthly',
          name: 'VIP月卡',
          description: '30天VIP特权，无限制解锁关卡',
          price: 2900,
          duration: 30,
          isActive: true,
        },
      ],
    },
  })
  async getVipPackages(): Promise<VipPackageDto[]> {
    return this.paymentService.getVipPackages();
  }

  @Post('create-payment')
  @ApiOperation({
    summary: '创建支付订单',
    description: `为指定用户和VIP套餐创建微信支付订单，返回小程序支付所需的参数。

订单配置：
- 订单有效期：30分钟（超时自动失效）
- 支持重复调用，不会产生重复订单

智能处理逻辑：
- 如果用户有相同套餐的未完成订单，直接返回该订单的支付参数
- 如果用户有不同套餐的未完成订单，自动取消旧订单并创建新订单
- 自动同步微信支付状态，确保订单状态准确性
- 订单过期后会自动失效，用户可重新创建订单`,
  })
  @ApiResponse({
    status: 201,
    description: '支付订单创建成功或返回现有订单支付参数',
    type: MiniProgramPaymentResponse,
    schema: {
      example: {
        appId: 'wx1234567890abcdef',
        timeStamp: '1640995200',
        nonceStr: 'abc123def456',
        package: 'prepay_id=wx123456789012345678901234567890',
        signType: 'RSA',
        paySign: 'signature_string_here',
      },
    },
  })
  @ApiResponse({ status: 400, description: '参数错误或创建订单失败' })
  @ApiResponse({ status: 404, description: '用户不存在或VIP套餐不存在' })
  async createPayment(
    @Body() body: { openid: string; packageId: string },
  ): Promise<MiniProgramPaymentResponse> {
    const { openid, packageId } = body;

    if (!openid || !packageId) {
      throw new BadRequestException('openid和packageId不能为空');
    }

    return this.paymentService.createPaymentOrder(openid, packageId);
  }

  @Get('payment-orders')
  @ApiOperation({ summary: '获取用户支付订单列表' })
  @ApiQuery({
    name: 'openid',
    description: '用户openid',
    example: 'wx_openid_123456',
  })
  @ApiResponse({
    status: 200,
    description: '订单列表获取成功',
    schema: {
      example: [
        {
          id: 'pay-order-12345678-1234',
          out_trade_no: 'ORDER_1640995200_1234',
          transaction_id: 'wx123456789012345678901234567890',
          description: 'VIP月卡',
          total: 2900,
          status: 'SUCCESS',
          vip_package_id: 'vip_monthly',
          paid_at: '2023-12-01 12:00:00',
          created_at: '2023-12-01 11:30:00',
        },
      ],
    },
  })
  async getPaymentOrders(@Query('openid') openid: string): Promise<any[]> {
    if (!openid) {
      throw new BadRequestException('openid不能为空');
    }

    // 通过openid获取用户信息
    const user = await this.userService.findByOpenid(openid);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return this.paymentService.getUserPaymentOrders(user.id);
  }

  @Get('payment-status/:out_trade_no')
  @ApiOperation({ summary: '查询支付订单状态' })
  @ApiParam({
    name: 'out_trade_no',
    description: '商户订单号',
    example: 'ORDER_1640995200_1234',
  })
  @ApiResponse({
    status: 200,
    description: '订单查询成功',
    schema: {
      example: {
        id: 'pay-order-12345678-1234',
        out_trade_no: 'ORDER_1640995200_1234',
        transaction_id: 'wx123456789012345678901234567890',
        description: 'VIP月卡',
        total: 2900,
        status: 'SUCCESS',
        vip_package_id: 'vip_monthly',
        paid_at: '2023-12-01 12:00:00',
        created_at: '2023-12-01 11:30:00',
      },
    },
  })
  @ApiResponse({ status: 404, description: '订单不存在' })
  async getPaymentStatus(
    @Param('out_trade_no') out_trade_no: string,
  ): Promise<any> {
    return this.paymentService.queryPaymentOrder(out_trade_no);
  }
}
