import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { CompleteLevelDto } from './dto/complete-level.dto';

@ApiTags('users')
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: '创建新用户' })
  @ApiResponse({ status: 201, description: '用户创建成功', type: UserResponseDto })
  @ApiResponse({ status: 400, description: 'openid已存在或请求参数无效' })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    return this.userService.create(createUserDto);
  }

  @Get()
  @ApiOperation({
    summary: '获取用户列表',
    description: '支持搜索、VIP状态过滤、日期范围过滤和分页'
  })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词（手机号、昵称、ID）' })
  @ApiQuery({ name: 'isVip', required: false, type: Boolean, description: 'VIP状态过滤' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期 (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期 (YYYY-MM-DD)' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码，默认1' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: '每页数量，默认20' })
  @ApiResponse({
    status: 200,
    description: '用户列表获取成功',
    schema: {
      type: 'object',
      properties: {
        users: { type: 'array', items: { $ref: '#/components/schemas/UserResponseDto' } },
        total: { type: 'number', description: '总用户数' }
      }
    }
  })
  async findAll(
    @Query('search') search?: string,
    @Query('isVip') isVip?: boolean,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
  ): Promise<{ users: UserResponseDto[]; total: number }> {
    return this.userService.findAll({
      search,
      isVip,
      startDate,
      endDate,
      page,
      pageSize
    });
  }

  @Get('by-openid')
  @ApiOperation({ summary: '根据openid获取用户信息' })
  @ApiQuery({ name: 'openid', description: '微信用户openid' })
  @ApiResponse({ status: 200, description: '用户信息获取成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async findByOpenid(@Query('openid') openid: string): Promise<UserResponseDto> {
    return this.userService.findByOpenid(openid);
  }

  @Get('by-phone')
  @ApiOperation({ summary: '根据手机号获取用户信息' })
  @ApiQuery({ name: 'phone', description: '用户手机号' })
  @ApiResponse({ status: 200, description: '用户信息获取成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async findByPhone(@Query('phone') phone: string): Promise<UserResponseDto> {
    return this.userService.findByPhone(phone);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID获取用户信息' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '用户信息获取成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async findOne(@Param('id') id: string): Promise<UserResponseDto> {
    return this.userService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新用户信息' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '用户信息更新成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    return this.userService.update(id, updateUserDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '删除用户' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 204, description: '用户删除成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.userService.remove(id);
  }

  @Post(':id/complete-level')
  @ApiOperation({ summary: '用户完成关卡' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '关卡完成记录成功', type: UserResponseDto })
  @ApiResponse({ status: 400, description: '关卡已完成或关卡不存在' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async completeLevel(
    @Param('id') id: string,
    @Body() completeLevelDto: CompleteLevelDto,
  ): Promise<UserResponseDto> {
    return this.userService.completeLevel(id, completeLevelDto);
  }

  @Post(':id/start-game')
  @ApiOperation({ summary: '用户开始游戏' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '游戏开始记录成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async startGame(@Param('id') id: string): Promise<UserResponseDto> {
    return this.userService.startGame(id);
  }

  @Get(':id/stats')
  @ApiOperation({ summary: '获取用户游戏统计' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ 
    status: 200, 
    description: '用户统计获取成功',
    schema: {
      type: 'object',
      properties: {
        totalGames: { type: 'number', description: '总游戏次数' },
        totalCompletions: { type: 'number', description: '总通关次数' },
        unlockedLevels: { type: 'number', description: '已解锁关卡数' },
        completedLevels: { type: 'number', description: '已完成关卡数' },
        completionRate: { type: 'number', description: '通关率(%)' },
      }
    }
  })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getUserStats(@Param('id') id: string) {
    return this.userService.getUserStats(id);
  }

  @Post(':id/reset-progress')
  @ApiOperation({ summary: '重置用户游戏进度' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '用户进度重置成功', type: UserResponseDto })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async resetUserProgress(@Param('id') id: string): Promise<UserResponseDto> {
    return this.userService.resetUserProgress(id);
  }
}
