# 开发环境配置
NODE_ENV=production

# 服务器配置
PORT=3001

# MongoDB 配置
# MongoDB Configuration
MONGODB_URI=*******************************************
MONGODB_DB_NAME=xxl_dev_db

# JWT 配置
JWT_SECRET=dev-secret-key-change-in-development
JWT_EXPIRES_IN=24h

# 微信小程序配置
# WEIXIN_APPID=wx410b77d5f0e31956
# WEIXIN_APP_SECRET=0b42ce2df7441c5747e22588d295e914
WEIXIN_APPID=wx280e45091c5ef854
WEIXIN_APP_SECRET=5f6c636eaa9d7753736a369426395559
WEIXIN_MCH_ID=1619236596                 # 商户号
WEIXIN_API_KEY=fhasiuofhasiohfioashfjioasjfkosa         # API密钥
WEIXIN_SERIAL_NO=6BC0F793CDA6445B153F8886D53CED0E359302A9   # 证书序列号
WEIXIN_NOTIFY_URL=https://localhost:3001/api/v1/payment/notify  # 支付回调URL（开发环境，生产环境需要使用公网域名）
WEIXIN_PRIVATE_KEY_PATH=./certs/apiclient_key.pem  # 商户私钥文件路径



# 调试配置
DEBUG=false

