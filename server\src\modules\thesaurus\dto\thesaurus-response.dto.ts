import { ApiProperty } from '@nestjs/swagger';

export class ThesaurusResponseDto {
  @ApiProperty({ description: '词库的唯一ID', example: 't1b2c3d4-e5f6-7890-1234-567890abcdef' })
  id: string;

  @ApiProperty({ description: '词库名称', example: '日常用语词库' })
  name: string;

  @ApiProperty({ description: '词库描述', example: '包含常见的日常对话用语', required: false })
  description?: string;

  @ApiProperty({
    description: '词库包含的词组ID列表',
    example: ['p1a2b3c4-e5f6-7890-1234-567890abcdef', 'p2a2b3c4-e5f6-7890-1234-567890abcdef'],
    type: [String],
  })
  phraseIds: string[];

  @ApiProperty({ description: '创建时间', example: '2023-10-28 10:30:00' })
  createdAt: string;

  @ApiProperty({ description: '最后更新时间', example: '2023-10-28 10:35:00' })
  updatedAt: string;
}