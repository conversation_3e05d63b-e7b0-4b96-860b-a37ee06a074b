import { PartialType } from '@nestjs/mapped-types';
import { CreateUserDto } from './create-user.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsArray, IsString, IsBoolean, IsDateString } from 'class-validator';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiProperty({ description: '用户当前已开启的关卡数', required: false })
  @IsNumber()
  @IsOptional()
  unlockedLevels?: number;

  @ApiProperty({ description: '用户已通关的关卡ID列表', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  completedLevelIds?: string[];

  @ApiProperty({ description: '用户总游戏次数', required: false })
  @IsNumber()
  @IsOptional()
  totalGames?: number;

  @ApiProperty({ description: '用户总通关次数', required: false })
  @IsNumber()
  @IsOptional()
  totalCompletions?: number;

  @ApiProperty({ description: 'VIP状态', required: false })
  @IsBoolean()
  @IsOptional()
  isVip?: boolean;

  @ApiProperty({ description: 'VIP过期时间', required: false })
  @IsDateString()
  @IsOptional()
  vipExpiresAt?: Date;

  @ApiProperty({ description: '每日解锁限制', required: false })
  @IsNumber()
  @IsOptional()
  dailyUnlockLimit?: number;

  @ApiProperty({ description: '当日解锁次数', required: false })
  @IsNumber()
  @IsOptional()
  dailyUnlockCount?: number;

  @ApiProperty({ description: '当日是否已分享', required: false })
  @IsBoolean()
  @IsOptional()
  dailyShared?: boolean;

  @ApiProperty({ description: '最后游戏日期（YYYY-MM-DD）', required: false })
  @IsString()
  @IsOptional()
  lastPlayDate?: string;

  @ApiProperty({ description: '总分享次数', required: false })
  @IsNumber()
  @IsOptional()
  totalShares?: number;
}
