"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const payment_service_1 = require("./payment.service");
const payment_controller_1 = require("./payment.controller");
const wechat_pay_service_1 = require("./services/wechat-pay.service");
const payment_order_entity_1 = require("./entities/payment-order.entity");
const user_module_1 = require("../user/user.module");
let PaymentModule = class PaymentModule {
};
exports.PaymentModule = PaymentModule;
exports.PaymentModule = PaymentModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: payment_order_entity_1.PaymentOrder.name, schema: payment_order_entity_1.PaymentOrderSchema },
                { name: payment_order_entity_1.VipPackage.name, schema: payment_order_entity_1.VipPackageSchema },
            ]),
            user_module_1.UserModule,
        ],
        controllers: [payment_controller_1.PaymentController],
        providers: [payment_service_1.PaymentService, wechat_pay_service_1.WechatPayService],
        exports: [payment_service_1.PaymentService, wechat_pay_service_1.WechatPayService],
    })
], PaymentModule);
//# sourceMappingURL=payment.module.js.map