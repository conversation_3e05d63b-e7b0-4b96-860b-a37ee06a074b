"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_service_1 = require("./user.service");
const create_user_dto_1 = require("./dto/create-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const user_response_dto_1 = require("./dto/user-response.dto");
const complete_level_dto_1 = require("./dto/complete-level.dto");
let UserController = class UserController {
    userService;
    constructor(userService) {
        this.userService = userService;
    }
    async create(createUserDto) {
        return this.userService.create(createUserDto);
    }
    async findAll(search, isVip, startDate, endDate, page, pageSize) {
        return this.userService.findAll({
            search,
            isVip,
            startDate,
            endDate,
            page,
            pageSize
        });
    }
    async findByOpenid(openid) {
        return this.userService.findByOpenid(openid);
    }
    async findByPhone(phone) {
        return this.userService.findByPhone(phone);
    }
    async findOne(id) {
        return this.userService.findOne(id);
    }
    async update(id, updateUserDto) {
        return this.userService.update(id, updateUserDto);
    }
    async remove(id) {
        return this.userService.remove(id);
    }
    async completeLevel(id, completeLevelDto) {
        return this.userService.completeLevel(id, completeLevelDto);
    }
    async startGame(id) {
        return this.userService.startGame(id);
    }
    async getUserStats(id) {
        return this.userService.getUserStats(id);
    }
    async resetUserProgress(id) {
        return this.userService.resetUserProgress(id);
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建新用户' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '用户创建成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'openid已存在或请求参数无效' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: '获取用户列表',
        description: '支持搜索、VIP状态过滤、日期范围过滤和分页'
    }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: '搜索关键词（手机号、昵称、ID）' }),
    (0, swagger_1.ApiQuery)({ name: 'isVip', required: false, type: Boolean, description: 'VIP状态过滤' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: '开始日期 (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: '结束日期 (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: '页码，默认1' }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', required: false, type: Number, description: '每页数量，默认20' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户列表获取成功',
        schema: {
            type: 'object',
            properties: {
                users: { type: 'array', items: { $ref: '#/components/schemas/UserResponseDto' } },
                total: { type: 'number', description: '总用户数' }
            }
        }
    }),
    __param(0, (0, common_1.Query)('search')),
    __param(1, (0, common_1.Query)('isVip')),
    __param(2, (0, common_1.Query)('startDate')),
    __param(3, (0, common_1.Query)('endDate')),
    __param(4, (0, common_1.Query)('page')),
    __param(5, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, String, String, Number, Number]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-openid'),
    (0, swagger_1.ApiOperation)({ summary: '根据openid获取用户信息' }),
    (0, swagger_1.ApiQuery)({ name: 'openid', description: '微信用户openid' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '用户信息获取成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Query)('openid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "findByOpenid", null);
__decorate([
    (0, common_1.Get)('by-phone'),
    (0, swagger_1.ApiOperation)({ summary: '根据手机号获取用户信息' }),
    (0, swagger_1.ApiQuery)({ name: 'phone', description: '用户手机号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '用户信息获取成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Query)('phone')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "findByPhone", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取用户信息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '用户信息获取成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户信息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '用户信息更新成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: '删除用户' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '用户删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/complete-level'),
    (0, swagger_1.ApiOperation)({ summary: '用户完成关卡' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '关卡完成记录成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '关卡已完成或关卡不存在' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, complete_level_dto_1.CompleteLevelDto]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "completeLevel", null);
__decorate([
    (0, common_1.Post)(':id/start-game'),
    (0, swagger_1.ApiOperation)({ summary: '用户开始游戏' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '游戏开始记录成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "startGame", null);
__decorate([
    (0, common_1.Get)(':id/stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户游戏统计' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '用户统计获取成功',
        schema: {
            type: 'object',
            properties: {
                totalGames: { type: 'number', description: '总游戏次数' },
                totalCompletions: { type: 'number', description: '总通关次数' },
                unlockedLevels: { type: 'number', description: '已解锁关卡数' },
                completedLevels: { type: 'number', description: '已完成关卡数' },
                completionRate: { type: 'number', description: '通关率(%)' },
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "getUserStats", null);
__decorate([
    (0, common_1.Post)(':id/reset-progress'),
    (0, swagger_1.ApiOperation)({ summary: '重置用户游戏进度' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '用户进度重置成功', type: user_response_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "resetUserProgress", null);
exports.UserController = UserController = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserController);
//# sourceMappingURL=user.controller.js.map