import { Model } from 'mongoose';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserDocument } from './entities/user.entity';
import { UserResponseDto } from './dto/user-response.dto';
import { CompleteLevelDto } from './dto/complete-level.dto';
import { LevelService } from '../level/level.service';
import { WeixinUserBindDto } from '../weixin/dto/weixin-user-bind.dto';
import { WeixinUserInfoDto, WeixinLevelDto } from '../weixin/dto/weixin-user-info.dto';
import { WeixinLevelDetailDto } from '../weixin/dto/weixin-level-detail.dto';
import { WeixinCompleteLevelDto, WeixinCompleteLevelResponseDto } from '../weixin/dto/weixin-complete-level.dto';
import { WeixinLoginDto, WeixinLoginResponseDto, WeixinPhoneBindDto } from '../weixin/dto/weixin-login.dto';
import { WeixinShareDto, WeixinShareResponseDto, WeixinDailyStatusDto } from '../weixin/dto/weixin-daily-play.dto';
export declare class UserService {
    private userModel;
    private readonly levelService;
    private readonly logger;
    constructor(userModel: Model<UserDocument>, levelService: LevelService);
    private cleanEmptyStrings;
    private generateUniqueUserId;
    private _mapToUserResponseDto;
    getUserEntity(id: string): Promise<UserDocument>;
    getUserByOpenid(openid: string): Promise<UserDocument | null>;
    getUserByPhone(phone: string): Promise<UserDocument | null>;
    create(createUserDto: CreateUserDto): Promise<UserResponseDto>;
    findAll(params?: {
        search?: string;
        isVip?: boolean;
        startDate?: string;
        endDate?: string;
        page?: number;
        pageSize?: number;
    }): Promise<{
        users: UserResponseDto[];
        total: number;
    }>;
    findOne(id: string): Promise<UserResponseDto>;
    findByOpenid(openid: string): Promise<UserResponseDto>;
    findByPhone(phone: string): Promise<UserResponseDto>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto>;
    remove(id: string): Promise<void>;
    completeLevel(userId: string, completeLevelDto: CompleteLevelDto): Promise<UserResponseDto>;
    startGame(userId: string): Promise<UserResponseDto>;
    getUserStats(userId: string): Promise<{
        totalGames: number;
        totalCompletions: number;
        unlockedLevels: number;
        completedLevels: number;
        completionRate: number;
    }>;
    resetUserProgress(userId: string): Promise<UserResponseDto>;
    private maskPhone;
    bindWeixinUser(bindDto: WeixinUserBindDto): Promise<WeixinUserInfoDto>;
    getWeixinUserInfo(openid: string): Promise<WeixinUserInfoDto>;
    getWeixinUserInfoById(id: string): Promise<WeixinUserInfoDto>;
    getWeixinLevelsWithProgress(openid: string): Promise<WeixinLevelDto[]>;
    getWeixinLevelDetail(openid: string, levelId: string): Promise<WeixinLevelDetailDto>;
    weixinCompleteLevel(completeLevelDto: WeixinCompleteLevelDto): Promise<WeixinCompleteLevelResponseDto>;
    weixinLogin(loginDto: WeixinLoginDto, weixinApiData: {
        openid: string;
        sessionKey: string;
        unionid?: string;
    }): Promise<WeixinLoginResponseDto>;
    weixinBindPhone(bindDto: WeixinPhoneBindDto): Promise<WeixinLoginResponseDto>;
    private checkAndResetDailyData;
    private checkAndUpdateVipStatus;
    private canUserUnlock;
    getWeixinDailyStatus(openid: string): Promise<WeixinDailyStatusDto>;
    weixinShare(shareDto: WeixinShareDto): Promise<WeixinShareResponseDto>;
}
