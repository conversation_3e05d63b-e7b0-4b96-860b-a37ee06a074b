export declare class WeixinLoginDto {
    code: string;
    phone?: string;
    nickname?: string;
    avatarUrl?: string;
}
export declare class WeixinLoginResponseDto {
    status: 'success' | 'need_bind';
    message: string;
    openid: string;
    sessionKey?: string;
    userInfo?: {
        id: string;
        maskedPhone?: string;
        nickname?: string;
        avatarUrl?: string;
        unlockedLevels: number;
        completedLevelIds: string[];
        totalGames: number;
        totalCompletions: number;
        lastPlayTime: string;
        createdAt: string;
    };
    unionid?: string;
}
export declare class WeixinPhoneBindDto {
    openid: string;
    phone: string;
    nickname?: string;
    avatarUrl?: string;
}
export declare class WeixinSessionCheckDto {
    openid: string;
    sessionKey: string;
}
