"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const phrase_module_1 = require("./modules/phrase/phrase.module");
const level_module_1 = require("./modules/level/level.module");
const common_module_1 = require("./common/common.module");
const thesaurus_module_1 = require("./modules/thesaurus/thesaurus.module");
const auth_module_1 = require("./modules/auth/auth.module");
const user_module_1 = require("./modules/user/user.module");
const share_module_1 = require("./modules/share/share.module");
const payment_module_1 = require("./modules/payment/payment.module");
const weixin_module_1 = require("./modules/weixin/weixin.module");
const settings_module_1 = require("./modules/settings/settings.module");
const app_service_1 = require("./app.service");
const configuration_1 = require("./config/configuration");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [configuration_1.default],
                validationSchema: configuration_1.configValidationSchema,
                envFilePath: [
                    `.env.${process.env.NODE_ENV || 'development'}`,
                    '.env',
                ],
                validationOptions: {
                    allowUnknown: true,
                    abortEarly: true,
                },
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    uri: configService.get('database.uri'),
                    dbName: configService.get('database.name'),
                    connectionFactory: (connection) => {
                        console.log(`✅ MongoDB 连接成功: ${configService.get('database.name')}`);
                        return connection;
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            common_module_1.CommonModule,
            level_module_1.LevelModule,
            phrase_module_1.PhraseModule,
            thesaurus_module_1.ThesaurusModule,
            auth_module_1.AuthModule,
            user_module_1.UserModule,
            share_module_1.ShareModule,
            payment_module_1.PaymentModule,
            weixin_module_1.WeixinModule,
            settings_module_1.SettingsModule,
        ],
        controllers: [],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map