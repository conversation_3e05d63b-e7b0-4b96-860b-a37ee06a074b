import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateThesaurusDto {
  @ApiProperty({ description: '词库名称', example: '日常用语词库' })
  @IsString()
  @IsNotEmpty({ message: '词库名称不能为空' })
  name: string;

  @ApiProperty({ description: '词库描述', example: '包含常见的日常对话用语', required: false })
  @IsString()
  @IsOptional()
  description?: string;
}