/**
 * VIP套餐初始化脚本
 * 
 * 初始化默认的VIP套餐数据
 * 
 * 运行方式：
 * node scripts/init-vip-packages.js
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || '**************************************************';
const DATABASE_NAME = process.env.MONGODB_DB_NAME || 'xxl_dev_db';

// 默认VIP套餐数据
const defaultVipPackages = [
  {
    id: 'vip_weekly',
    name: 'VIP周卡',
    description: '7天VIP特权，无限制解锁关卡，畅享游戏乐趣',
    price: 800, // 8元
    duration: 7,
    sortOrder: 1,
    isActive: true,
  },
  {
    id: 'vip_monthly',
    name: 'VIP月卡',
    description: '30天VIP特权，无限制解锁关卡，性价比之选',
    price: 2900, // 29元
    duration: 30,
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 'vip_quarterly',
    name: 'VIP季卡',
    description: '90天VIP特权，无限制解锁关卡，超值优惠',
    price: 6900, // 69元
    duration: 90,
    sortOrder: 3,
    isActive: true,
  },
  {
    id: 'vip_yearly',
    name: 'VIP年卡',
    description: '365天VIP特权，无限制解锁关卡，最划算选择',
    price: 19900, // 199元
    duration: 365,
    sortOrder: 4,
    isActive: true,
  },
];

async function initVipPackages() {
  let client;
  
  try {
    console.log('🚀 开始初始化VIP套餐...');
    console.log(`📍 连接数据库: ${MONGODB_URI}`);
    
    // 连接数据库
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const vipPackagesCollection = db.collection('vippackages');
    
    // 检查是否已有VIP套餐数据
    const existingPackages = await vipPackagesCollection.find({}).toArray();
    console.log(`📊 现有VIP套餐数量: ${existingPackages.length}`);
    
    let insertedCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const packageData of defaultVipPackages) {
      const existingPackage = await vipPackagesCollection.findOne({ id: packageData.id });
      
      if (existingPackage) {
        // 更新现有套餐（保持价格和状态不变，只更新描述等信息）
        const updateResult = await vipPackagesCollection.updateOne(
          { id: packageData.id },
          {
            $set: {
              name: packageData.name,
              description: packageData.description,
              duration: packageData.duration,
              sortOrder: packageData.sortOrder,
              updatedAt: new Date(),
            }
          }
        );
        
        if (updateResult.modifiedCount > 0) {
          updatedCount++;
          console.log(`📝 更新VIP套餐: ${packageData.id} - ${packageData.name}`);
        } else {
          skippedCount++;
          console.log(`⏭️  跳过VIP套餐: ${packageData.id} - ${packageData.name} (无需更新)`);
        }
      } else {
        // 插入新套餐
        const newPackage = {
          ...packageData,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        await vipPackagesCollection.insertOne(newPackage);
        insertedCount++;
        console.log(`✅ 创建VIP套餐: ${packageData.id} - ${packageData.name} - ¥${(packageData.price / 100).toFixed(2)}`);
      }
    }
    
    // 显示所有VIP套餐
    console.log('\n📋 当前VIP套餐列表:');
    const allPackages = await vipPackagesCollection
      .find({ isActive: true })
      .sort({ sortOrder: 1 })
      .toArray();
    
    allPackages.forEach(pkg => {
      const price = (pkg.price / 100).toFixed(2);
      const status = pkg.isActive ? '✅ 启用' : '❌ 禁用';
      console.log(`   ${pkg.sortOrder}. ${pkg.name} - ¥${price} (${pkg.duration}天) ${status}`);
      console.log(`      ${pkg.description}`);
    });
    
    console.log('\n📈 初始化统计:');
    console.log(`   - 新增套餐: ${insertedCount}`);
    console.log(`   - 更新套餐: ${updatedCount}`);
    console.log(`   - 跳过套餐: ${skippedCount}`);
    console.log(`   - 总套餐数: ${allPackages.length}`);
    console.log('✅ VIP套餐初始化完成！');
    
  } catch (error) {
    console.error('❌ 初始化VIP套餐失败:', error);
    throw error;
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  try {
    await initVipPackages();
    process.exit(0);
  } catch (error) {
    console.error('💥 初始化失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { initVipPackages };
