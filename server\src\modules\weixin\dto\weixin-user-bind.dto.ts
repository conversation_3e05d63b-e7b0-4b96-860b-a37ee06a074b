import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, Matches } from 'class-validator';

export class WeixinUserBindDto {
  @ApiProperty({ description: '微信用户的openid', example: 'wx_openid_123456' })
  @IsString()
  @IsNotEmpty()
  openid: string;

  @ApiProperty({ description: '用户手机号', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的手机号码' })
  phone: string;

  @ApiProperty({ description: '用户昵称', required: false, example: '微信用户' })
  @IsString()
  @IsOptional()
  nickname?: string;

  @ApiProperty({ description: '用户头像URL', required: false, example: 'https://wx.qlogo.cn/mmopen/...' })
  @IsString()
  @IsOptional()
  avatarUrl?: string;
}
