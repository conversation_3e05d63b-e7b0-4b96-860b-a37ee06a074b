import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { PaymentService } from './payment.service';
import { WechatPayService } from './services/wechat-pay.service';
import { MiniProgramPaymentResponse, VipPackageDto, PaymentNotifyDto, CreateVipPackageDto, UpdateVipPackageDto } from './dto/payment.dto';

@ApiTags('支付管理')
@Controller('payment')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(
    private readonly paymentService: PaymentService,
    private readonly wechatPayService: WechatPayService,
  ) {}

  @Get('config/wechat')
  @ApiOperation({
    summary: '获取微信支付配置状态（调试用）',
    description: '检查微信支付相关配置是否正确，用于调试支付问题'
  })
  @ApiResponse({
    status: 200,
    description: '微信支付配置状态',
    schema: {
      example: {
        appId: 'wx280e45091c5ef854',
        mchId: '1619236596',
        hasApiKey: true,
        hasSerialNo: true,
        hasNotifyUrl: true,
        hasPrivateKey: true,
        notifyUrl: 'https://localhost:3001/api/v1/payment/notify',
        isConfigured: true,
        timestamp: '2025-06-21T12:30:00.000Z'
      }
    }
  })
  async getWechatPayConfig(): Promise<any> {
    return this.wechatPayService.getConfigStatus();
  }

  @Get('vip-packages')
  @ApiOperation({ summary: '获取VIP套餐列表' })
  @ApiResponse({
    status: 200,
    description: 'VIP套餐列表获取成功',
    type: [VipPackageDto],
    schema: {
      example: [
        {
          id: 'vip_monthly',
          name: 'VIP月卡',
          description: '30天VIP特权，无限制解锁关卡',
          price: 2900,
          duration: 30,
          sortOrder: 1,
          isActive: true,
          createdAt: '2023-12-01 10:00:00',
          updatedAt: '2023-12-01 10:00:00'
        }
      ]
    }
  })
  async getVipPackages(): Promise<VipPackageDto[]> {
    return this.paymentService.getAllVipPackages();
  }

  @Get('vip-packages/:id')
  @ApiOperation({ summary: '根据ID获取VIP套餐详情' })
  @ApiParam({ name: 'id', description: 'VIP套餐ID', example: 'vip_monthly' })
  @ApiResponse({
    status: 200,
    description: 'VIP套餐详情获取成功',
    type: VipPackageDto
  })
  @ApiResponse({ status: 404, description: 'VIP套餐不存在' })
  async getVipPackageById(@Param('id') id: string): Promise<VipPackageDto> {
    return this.paymentService.getVipPackageById(id);
  }

  @Post('vip-packages')
  @ApiOperation({ summary: '创建VIP套餐（ID自动生成）' })
  @ApiResponse({
    status: 201,
    description: 'VIP套餐创建成功，ID自动生成',
    type: VipPackageDto,
    schema: {
      example: {
        id: 'vip_monthly_30d_a1b2',
        name: 'VIP月卡',
        description: '30天VIP特权，无限制解锁关卡',
        price: 2900,
        duration: 30,
        sortOrder: 1,
        isActive: true,
        createdAt: '2023-12-01 10:00:00',
        updatedAt: '2023-12-01 10:00:00'
      }
    }
  })
  @ApiResponse({ status: 400, description: '参数错误' })
  async createVipPackage(@Body() createDto: CreateVipPackageDto): Promise<VipPackageDto> {
    return this.paymentService.createVipPackage(createDto);
  }

  @Put('vip-packages/:id')
  @ApiOperation({ summary: '更新VIP套餐' })
  @ApiParam({ name: 'id', description: 'VIP套餐ID', example: 'vip_monthly' })
  @ApiResponse({
    status: 200,
    description: 'VIP套餐更新成功',
    type: VipPackageDto
  })
  @ApiResponse({ status: 404, description: 'VIP套餐不存在' })
  @ApiResponse({ status: 400, description: '参数错误' })
  async updateVipPackage(
    @Param('id') id: string,
    @Body() updateDto: UpdateVipPackageDto
  ): Promise<VipPackageDto> {
    return this.paymentService.updateVipPackage(id, updateDto);
  }

  @Delete('vip-packages/:id')
  @ApiOperation({ summary: '删除VIP套餐' })
  @ApiParam({ name: 'id', description: 'VIP套餐ID', example: 'vip_monthly' })
  @ApiResponse({ status: 200, description: 'VIP套餐删除成功' })
  @ApiResponse({ status: 404, description: 'VIP套餐不存在' })
  @ApiResponse({ status: 400, description: '存在相关订单，无法删除' })
  async deleteVipPackage(@Param('id') id: string): Promise<{ message: string }> {
    await this.paymentService.deleteVipPackage(id);
    return { message: 'VIP套餐删除成功' };
  }

  @Post('create-order')
  @ApiOperation({ summary: '创建支付订单' })
  @ApiResponse({
    status: 201,
    description: '支付订单创建成功，返回小程序支付参数',
    type: MiniProgramPaymentResponse,
    schema: {
      example: {
        appId: 'wx1234567890abcdef',
        timeStamp: '1640995200',
        nonceStr: 'abc123def456',
        package: 'prepay_id=wx123456789012345678901234567890',
        signType: 'RSA',
        paySign: 'signature_string_here'
      }
    }
  })
  @ApiResponse({ status: 400, description: '参数错误或创建订单失败' })
  @ApiResponse({ status: 404, description: '用户不存在或VIP套餐不存在' })
  async createPaymentOrder(
    @Body() body: { openid: string; packageId: string }
  ): Promise<MiniProgramPaymentResponse> {
    const { openid, packageId } = body;
    
    if (!openid || !packageId) {
      throw new BadRequestException('openid和packageId不能为空');
    }

    return this.paymentService.createPaymentOrder(openid, packageId);
  }

  @Post('notify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '微信支付回调通知' })
  @ApiResponse({ status: 200, description: '回调处理成功' })
  @ApiResponse({ status: 400, description: '回调处理失败' })
  async handlePaymentNotify(
    @Headers() headers: any,
    @Body() body: PaymentNotifyDto
  ): Promise<{ code: string; message: string }> {
    try {
      this.logger.log('收到微信支付回调通知');

      // 验证签名
      const bodyStr = JSON.stringify(body);
      const isValidSignature = this.wechatPayService.verifyNotifySignature(headers, bodyStr);
      
      if (!isValidSignature) {
        this.logger.error('微信支付回调签名验证失败');
        throw new BadRequestException('签名验证失败');
      }

      // 解密回调数据
      const decryptedData = this.wechatPayService.decryptNotifyData(body.resource);
      
      // 处理支付结果
      await this.paymentService.handlePaymentNotify(decryptedData);

      return { code: 'SUCCESS', message: '处理成功' };
    } catch (error) {
      this.logger.error('处理微信支付回调失败:', error);
      return { code: 'FAIL', message: '处理失败' };
    }
  }

  @Get('query/:out_trade_no')
  @ApiOperation({ summary: '查询支付订单状态' })
  @ApiParam({ name: 'out_trade_no', description: '商户订单号', example: 'ORDER_1640995200_1234' })
  @ApiResponse({
    status: 200,
    description: '订单查询成功',
    schema: {
      example: {
        id: 'pay-order-12345678-1234',
        out_trade_no: 'ORDER_1640995200_1234',
        transaction_id: 'wx123456789012345678901234567890',
        description: 'VIP月卡',
        total: 2900,
        status: 'SUCCESS',
        vip_package_id: 'vip_monthly',
        paid_at: '2023-12-01 12:00:00',
        created_at: '2023-12-01 11:30:00'
      }
    }
  })
  @ApiResponse({ status: 404, description: '订单不存在' })
  async queryPaymentOrder(@Param('out_trade_no') out_trade_no: string): Promise<any> {
    return this.paymentService.queryPaymentOrder(out_trade_no);
  }

  @Post('refresh/:out_trade_no')
  @ApiOperation({
    summary: '手动刷新支付订单状态',
    description: '主动查询微信支付状态并同步到本地订单，用于调试支付状态同步问题'
  })
  @ApiParam({ name: 'out_trade_no', description: '商户订单号', example: 'ORDER_1640995200_1234' })
  @ApiResponse({
    status: 200,
    description: '状态刷新成功',
    schema: {
      example: {
        message: '订单状态已刷新',
        localStatus: 'SUCCESS',
        wechatStatus: 'SUCCESS',
        synced: true
      }
    }
  })
  @ApiResponse({ status: 404, description: '订单不存在' })
  async refreshPaymentOrderStatus(@Param('out_trade_no') out_trade_no: string): Promise<any> {
    return this.paymentService.refreshPaymentOrderStatus(out_trade_no);
  }

  @Get('orders')
  @ApiOperation({ summary: '获取支付订单列表（管理员）' })
  @ApiQuery({ name: 'page', description: '页码', example: 1, required: false })
  @ApiQuery({ name: 'pageSize', description: '每页数量', example: 20, required: false })
  @ApiQuery({ name: 'search', description: '搜索关键词（订单号、用户ID、描述）', required: false })
  @ApiQuery({ name: 'status', description: '订单状态', enum: ['PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED'], required: false })
  @ApiQuery({ name: 'startDate', description: '开始日期', example: '2023-12-01', required: false })
  @ApiQuery({ name: 'endDate', description: '结束日期', example: '2023-12-31', required: false })
  @ApiQuery({ name: 'userId', description: '用户ID（获取特定用户订单）', required: false })
  @ApiResponse({
    status: 200,
    description: '订单列表获取成功',
    schema: {
      example: {
        orders: [
          {
            id: 'pay-order-12345678-1234',
            userId: '12345678',
            openid: 'wx_openid_123456',
            out_trade_no: 'ORDER_1640995200_1234',
            transaction_id: 'wx123456789012345678901234567890',
            description: 'VIP月卡',
            total: 2900,
            status: 'SUCCESS',
            vip_package_id: 'vip_monthly',
            paid_at: '2023-12-01 12:00:00',
            expires_at: '2023-12-01 12:30:00',
            created_at: '2023-12-01 11:30:00',
            updated_at: '2023-12-01 12:00:00'
          }
        ],
        total: 1,
        page: 1,
        pageSize: 20,
        totalPages: 1
      }
    }
  })
  async getPaymentOrders(
    @Query('page') page: string = '1',
    @Query('pageSize') pageSize: string = '20',
    @Query('search') search?: string,
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userId') userId?: string,
  ): Promise<any> {
    const pageNum = parseInt(page, 10);
    const pageSizeNum = parseInt(pageSize, 10);

    return this.paymentService.getPaymentOrdersList({
      page: pageNum,
      pageSize: pageSizeNum,
      search,
      status,
      startDate,
      endDate,
      userId,
    });
  }

  @Get('orders/user/:userId')
  @ApiOperation({ summary: '获取用户支付订单列表' })
  @ApiParam({ name: 'userId', description: '用户ID', example: '12345678' })
  @ApiResponse({
    status: 200,
    description: '订单列表获取成功',
    schema: {
      example: [
        {
          id: 'pay-order-12345678-1234',
          out_trade_no: 'ORDER_1640995200_1234',
          transaction_id: 'wx123456789012345678901234567890',
          description: 'VIP月卡',
          total: 2900,
          status: 'SUCCESS',
          vip_package_id: 'vip_monthly',
          paid_at: '2023-12-01 12:00:00',
          created_at: '2023-12-01 11:30:00'
        }
      ]
    }
  })
  async getUserPaymentOrders(@Param('userId') userId: string): Promise<any[]> {
    return this.paymentService.getUserPaymentOrders(userId);
  }

  @Get('orders/stats')
  @ApiOperation({ summary: '获取支付订单统计信息' })
  @ApiQuery({ name: 'startDate', description: '开始日期', example: '2023-12-01', required: false })
  @ApiQuery({ name: 'endDate', description: '结束日期', example: '2023-12-31', required: false })
  @ApiResponse({
    status: 200,
    description: '统计信息获取成功',
    schema: {
      example: {
        total: 100,
        pending: 10,
        success: 80,
        failed: 8,
        cancelled: 2,
        refunded: 0,
        totalAmount: 290000,
        successAmount: 232000,
        pendingAmount: 29000,
        successRate: 80.0,
        avgOrderAmount: 2900
      }
    }
  })
  async getPaymentOrderStats(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<any> {
    return this.paymentService.getPaymentOrderStats({ startDate, endDate });
  }

  @Get('orders/:id')
  @ApiOperation({ summary: '根据ID获取支付订单详情' })
  @ApiParam({ name: 'id', description: '订单ID', example: 'pay-order-12345678-1234' })
  @ApiResponse({
    status: 200,
    description: '订单详情获取成功',
    schema: {
      example: {
        id: 'pay-order-12345678-1234',
        userId: '12345678',
        openid: 'wx_openid_123456',
        out_trade_no: 'ORDER_1640995200_1234',
        transaction_id: 'wx123456789012345678901234567890',
        description: 'VIP月卡',
        total: 2900,
        status: 'SUCCESS',
        vip_package_id: 'vip_monthly',
        prepay_id: 'wx201410272009395522657a690389285100',
        detail: 'VIP月卡详情',
        attach: '{"userId":"12345678","packageId":"vip_monthly"}',
        paid_at: '2023-12-01 12:00:00',
        expires_at: '2023-12-01 12:30:00',
        created_at: '2023-12-01 11:30:00',
        updated_at: '2023-12-01 12:00:00'
      }
    }
  })
  @ApiResponse({ status: 404, description: '订单不存在' })
  async getPaymentOrderById(@Param('id') id: string): Promise<any> {
    return this.paymentService.getPaymentOrderById(id);
  }

  @Post('cancel/:out_trade_no')
  @ApiOperation({ summary: '取消支付订单' })
  @ApiParam({ name: 'out_trade_no', description: '商户订单号', example: 'ORDER_1640995200_1234' })
  @ApiResponse({ status: 200, description: '订单取消成功' })
  @ApiResponse({ status: 404, description: '订单不存在或无法取消' })
  async cancelPaymentOrder(
    @Param('out_trade_no') out_trade_no: string,
    @Body() body: { userId: string }
  ): Promise<{ message: string }> {
    const { userId } = body;

    if (!userId) {
      throw new BadRequestException('userId不能为空');
    }

    await this.paymentService.cancelPaymentOrder(out_trade_no, userId);
    return { message: '订单取消成功' };
  }
}
