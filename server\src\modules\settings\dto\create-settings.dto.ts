import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsUrl } from 'class-validator';

export class CreateSettingsDto {
  @ApiProperty({ description: '设置键名', example: 'help_url' })
  @IsString()
  key: string;

  @ApiProperty({ description: '设置值', example: 'https://help.example.com' })
  @IsString()
  value: string;

  @ApiProperty({ description: '设置描述', example: '帮助页面链接', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: '设置类型', 
    enum: ['string', 'number', 'boolean', 'url'],
    example: 'url'
  })
  @IsEnum(['string', 'number', 'boolean', 'url'])
  type: string;
}
