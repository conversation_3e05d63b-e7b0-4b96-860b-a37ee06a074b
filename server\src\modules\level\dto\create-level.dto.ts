import { IsString, IsNotEmpty, IsOptional, IsNumber, Min, <PERSON>, IsArray, ArrayNotEmpty, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateLevelDto {
  @IsString()
  @IsNotEmpty({ message: '关卡名称不能为空' })
  name: string;

  @IsNumber({}, { message: '难度必须是数字' })
  @Min(1, { message: '难度最小为1' })
  @Max(5, { message: '难度最大为5' })
  @IsNotEmpty({ message: '关卡难度不能为空' })
  difficulty: number;

  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '关联的词库ID列表', type: [String], example: ['uuid1-thesaurus-xxx', 'uuid2-thesaurus-yyy'], required: false})
  @IsArray()
  @IsOptional()
  @IsUUID('all', { each: true, message: '每个词库ID都必须是有效的UUID格式' })
  thesaurusIds?: string[];

  @ApiProperty({ description: '直接选择的词组ID列表', type: [String], example: ['uuid1-phrase-xxx', 'uuid2-phrase-yyy'], required: false})
  @IsArray()
  @IsOptional()
  @IsUUID('all', { each: true, message: '每个词组ID都必须是有效的UUID格式' })
  phraseIds?: string[];
}