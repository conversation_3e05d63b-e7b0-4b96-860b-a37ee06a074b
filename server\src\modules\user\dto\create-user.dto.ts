import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, Matches, IsNotEmpty } from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ description: '用户手机号', example: '13800138000', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '手机号不能为空字符串' })
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的手机号码' })
  phone?: string;

  @ApiProperty({ description: '微信用户的openid', required: false })
  @IsString()
  @IsOptional()
  openid?: string;

  @ApiProperty({ description: '用户昵称', required: false })
  @IsString()
  @IsOptional()
  nickname?: string;

  @ApiProperty({ description: '用户头像URL', required: false })
  @IsString()
  @IsOptional()
  avatarUrl?: string;
}
