import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsArray, ArrayMinSize } from 'class-validator';

export class CreatePhraseDto {
  @ApiProperty({ description: '词组文本', example: 'Hello World' })
  @IsString()
  @IsNotEmpty({ message: '词组文本不能为空' })
  text: string;

  @ApiProperty({ description: '词组含义', example: '你好，世界' })
  @IsString()
  @IsNotEmpty({ message: '词组含义不能为空' })
  meaning: string;

  @ApiProperty({ description: '例句', example: 'When you start programming, the first thing you often do is print "Hello World".', required: false })
  @IsString()
  @IsOptional()
  exampleSentence?: string;

  @ApiProperty({ description: '标签', example: ['greeting', 'common'], required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  @IsOptional()
  tags?: string[];
}