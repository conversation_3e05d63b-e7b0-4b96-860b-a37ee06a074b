import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON><PERSON>Controller } from './weixin.controller';
import { UserModule } from '../user/user.module';
import { LevelModule } from '../level/level.module';
import { ShareModule } from '../share/share.module';
import { PaymentModule } from '../payment/payment.module';
import { SettingsModule } from '../settings/settings.module';
import { WeixinApiService } from './services/weixin-api.service';

@Module({
  imports: [UserModule, LevelModule, ShareModule, PaymentModule, SettingsModule],
  controllers: [WeixinController],
  providers: [WeixinApiService],
  exports: [WeixinApiService],
})
export class WeixinModule {}
