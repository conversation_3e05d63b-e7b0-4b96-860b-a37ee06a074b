import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type ShareConfigDocument = ShareConfig & Document;

@Schema({ timestamps: true })
export class ShareConfig {
  @ApiProperty({ description: '分享配置的唯一ID', example: 'share-config-001' })
  @Prop({ required: true, unique: true })
  id: string;

  @ApiProperty({ description: '分享配置名称', example: '默认分享配置' })
  @Prop({ required: true })
  name: string;

  @ApiProperty({ description: '分享标题', example: '一起来挑战词汇游戏！' })
  @Prop({ required: true })
  title: string;

  @ApiProperty({ description: '分享路径', example: '/pages/index/index' })
  @Prop({ required: true })
  path: string;

  @ApiProperty({ description: '分享图片URL', required: false })
  @Prop()
  imageUrl?: string;

  @ApiProperty({ description: '分享描述', required: false })
  @Prop()
  description?: string;

  @ApiProperty({ description: '分享类型', example: 'default' })
  @Prop({ required: true, default: 'default' })
  type: string;

  @ApiProperty({ description: '是否启用', example: true })
  @Prop({ required: true, default: true })
  isActive: boolean;

  @ApiProperty({ description: '排序权重', example: 1 })
  @Prop({ required: true, default: 1 })
  sortOrder: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const ShareConfigSchema = SchemaFactory.createForClass(ShareConfig);

// 为了向后兼容，保留ShareConfigEntity类型别名
export interface ShareConfigEntity {
  id: string;
  name: string;
  title: string;
  path: string;
  imageUrl?: string;
  description?: string;
  type: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}
