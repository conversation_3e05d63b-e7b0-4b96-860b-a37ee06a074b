import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type AdminUserDocument = AdminUser & Document;

@Schema({ timestamps: true })
export class AdminUser {
  @ApiProperty({ description: '管理员用户ID', example: 'admin001' })
  @Prop({ required: true, unique: true })
  userId: string;

  @ApiProperty({ description: '用户名', example: 'admin' })
  @Prop({ required: true, unique: true })
  username: string;

  @ApiProperty({ description: '密码哈希值' })
  @Prop({ required: true })
  password: string;

  @ApiProperty({ description: '用户角色', example: ['admin'] })
  @Prop({ type: [String], default: ['admin'] })
  roles: string[];

  @ApiProperty({ description: '是否激活', example: true })
  @Prop({ default: true })
  isActive: boolean;

  @ApiProperty({ description: '最后登录时间' })
  @Prop()
  lastLoginAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

export const AdminUserSchema = SchemaFactory.createForClass(AdminUser);
