import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ShareService } from './modules/share/share.service';

@Injectable()
export class AppService implements OnModuleInit {
  private readonly logger = new Logger(AppService.name);

  constructor(private readonly shareService: ShareService) {}

  async onModuleInit() {
    this.logger.log('🚀 应用初始化开始...');
    
    try {
      // 初始化默认分享配置
      await this.shareService.initializeDefaultConfig();
      
      this.logger.log('✅ 应用初始化完成');
    } catch (error) {
      this.logger.error('❌ 应用初始化失败:', error);
    }
  }

  getHello(): string {
    return 'Hello World!';
  }
}
