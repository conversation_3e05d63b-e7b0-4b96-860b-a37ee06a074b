import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsNumber, IsNotEmpty, IsUrl } from 'class-validator';

export class CreateShareConfigDto {
  @ApiProperty({ description: '分享配置名称', example: '默认分享配置' })
  @IsString()
  @IsNotEmpty({ message: '配置名称不能为空' })
  name: string;

  @ApiProperty({ description: '分享标题', example: '一起来挑战词汇游戏！' })
  @IsString()
  @IsNotEmpty({ message: '分享标题不能为空' })
  title: string;

  @ApiProperty({ description: '分享路径', example: '/pages/index/index' })
  @IsString()
  @IsNotEmpty({ message: '分享路径不能为空' })
  path: string;

  @ApiProperty({ description: '分享图片URL', example: 'https://example.com/share.jpg', required: false })
  @IsOptional()
  @IsUrl({}, { message: '请输入有效的图片URL' })
  imageUrl?: string;

  @ApiProperty({ description: '分享描述', example: '挑战你的词汇量，看看你能通过多少关！', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '分享类型', example: 'default', required: false })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({ description: '是否启用', example: true, required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: '排序权重', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序权重必须是数字' })
  sortOrder?: number;
}
