"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinModule = void 0;
const common_1 = require("@nestjs/common");
const weixin_controller_1 = require("./weixin.controller");
const user_module_1 = require("../user/user.module");
const level_module_1 = require("../level/level.module");
const share_module_1 = require("../share/share.module");
const payment_module_1 = require("../payment/payment.module");
const settings_module_1 = require("../settings/settings.module");
const weixin_api_service_1 = require("./services/weixin-api.service");
let WeixinModule = class WeixinModule {
};
exports.WeixinModule = WeixinModule;
exports.WeixinModule = WeixinModule = __decorate([
    (0, common_1.Module)({
        imports: [user_module_1.UserModule, level_module_1.LevelModule, share_module_1.ShareModule, payment_module_1.PaymentModule, settings_module_1.SettingsModule],
        controllers: [weixin_controller_1.WeixinController],
        providers: [weixin_api_service_1.WeixinApiService],
        exports: [weixin_api_service_1.WeixinApiService],
    })
], WeixinModule);
//# sourceMappingURL=weixin.module.js.map